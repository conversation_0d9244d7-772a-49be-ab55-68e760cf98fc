import { test, expect } from '@playwright/test';

test.describe('Comprehensive Production Diagnosis', () => {
    test('Complete Platform Functionality Check - Single Session', async ({ page }) => {
        console.log('🔍 Starting comprehensive production diagnosis...');
        
        // Navigate to production site
        await page.goto('https://royalty.technology');
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(3000);
        
        console.log('📄 Page loaded, checking content...');

        // Get page title and basic info
        const title = await page.title();
        const url = page.url();
        console.log(`   📄 Title: ${title}`);
        console.log(`   🌐 URL: ${url}`);

        // Get all visible text on page
        const bodyText = await page.locator('body').textContent();
        console.log(`   📝 Page text length: ${bodyText.length} characters`);
        console.log(`   📝 First 200 characters: ${bodyText.substring(0, 200)}...`);

        // Check for any error messages on screen
        const errorMessages = await page.locator('text=/failed to load|error|not found|unable to/i').allTextContents();
        if (errorMessages.length > 0) {
            console.log('❌ ERROR MESSAGES DETECTED:');
            errorMessages.forEach(msg => console.log(`   - ${msg}`));
        }

        // Check what form elements are available
        const emailInputs = await page.locator('input[type="email"], input[name="email"]').count();
        const passwordInputs = await page.locator('input[type="password"], input[name="password"]').count();
        const submitButtons = await page.locator('button[type="submit"], button:has-text("Sign In"), button:has-text("Login")').count();
        console.log(`   📧 Email inputs found: ${emailInputs}`);
        console.log(`   🔒 Password inputs found: ${passwordInputs}`);
        console.log(`   🔘 Submit buttons found: ${submitButtons}`);
        
        // Check console errors
        const consoleErrors = [];
        page.on('console', msg => {
            if (msg.type() === 'error') {
                consoleErrors.push(msg.text());
                console.log(`🚨 Console Error: ${msg.text()}`);
            }
        });
        
        // Check network failures
        const networkFailures = [];
        page.on('response', response => {
            if (response.status() >= 400) {
                networkFailures.push(`${response.status()} ${response.url()}`);
                console.log(`🌐 Network Error: ${response.status()} ${response.url()}`);
            }
        });
        
        console.log('🔐 Checking authentication status...');

        // Check if already logged in
        const isLoggedIn = await page.locator('text=/dashboard|profile|logout/i').count() > 0;
        console.log(`   🔓 Already logged in: ${isLoggedIn}`);

        if (!isLoggedIn && emailInputs > 0 && passwordInputs > 0) {
            console.log('   📝 Attempting to fill login form...');
            await page.fill('input[type="email"], input[name="email"]', '<EMAIL>');
            await page.fill('input[type="password"], input[name="password"]', 'TestPassword123!');

            if (submitButtons > 0) {
                console.log('   🔘 Trying different approaches to click login button...');

                // Try clicking visible buttons first
                const visibleButtons = await page.locator('button:visible:has-text("Sign In")').count();
                console.log(`   👁️ Visible Sign In buttons: ${visibleButtons}`);

                if (visibleButtons > 0) {
                    await page.click('button:visible:has-text("Sign In")');
                } else {
                    // Force click the first button
                    console.log('   🔧 Force clicking first Sign In button...');
                    await page.locator('button:has-text("Sign In")').first().click({ force: true });
                }

                await page.waitForLoadState('networkidle');
                await page.waitForTimeout(3000);
            } else {
                console.log('   ❌ No submit button found!');
            }
        } else if (!isLoggedIn) {
            console.log('   ❌ Login form not found - missing email/password inputs');
        }
        
        console.log('📸 Checking post-auth status...');
        const currentUrl = page.url();
        const currentTitle = await page.title();
        console.log(`   🌐 Current URL: ${currentUrl}`);
        console.log(`   📄 Current Title: ${currentTitle}`);

        // Check if we're still on login page (authentication failed)
        if (currentUrl.includes('/login')) {
            console.log('⚠️ Still on login page - authentication may have failed');
            const loginPageText = await page.locator('body').textContent();
            const authErrors = loginPageText.match(/(invalid|incorrect|failed|error|wrong)/gi);
            if (authErrors) {
                console.log('❌ Possible authentication errors:');
                authErrors.forEach(err => console.log(`   - ${err}`));
            }
        }
        
        // Test 1: Dashboard/Home functionality
        console.log('🏠 Testing Dashboard functionality...');
        const dashboardElements = await page.locator('[data-testid*="dashboard"], .dashboard').count();
        const dashboardText = await page.locator('text=dashboard').count();
        const interactiveElements = await page.locator('button, input, select, textarea, a[href]').count();
        console.log(`   📊 Dashboard elements: ${dashboardElements}`);
        console.log(`   📊 Dashboard text: ${dashboardText}`);
        console.log(`   🎯 Interactive elements: ${interactiveElements}`);

        // Check current page content
        const currentBodyText = await page.locator('body').textContent();
        console.log(`   📝 Current page text length: ${currentBodyText.length} characters`);
        if (currentBodyText.includes('failed to load') || currentBodyText.includes('error')) {
            console.log('   ❌ ERROR DETECTED ON CURRENT PAGE');
            const errorText = currentBodyText.match(/(failed to load[^.]*|error[^.]*)/gi);
            if (errorText) errorText.forEach(err => console.log(`      - ${err}`));
        }
        
        // Test 2: Navigation to Track page
        console.log('📋 Testing Track page...');
        const trackLinks = await page.locator('text=track, a[href*="track"]').count();
        console.log(`   🔗 Track links found: ${trackLinks}`);

        if (trackLinks > 0) {
            await page.click('text=track, a[href*="track"]');
            await page.waitForLoadState('networkidle');
            await page.waitForTimeout(2000);

            const trackPageText = await page.locator('body').textContent();
            console.log(`   📝 Track page text length: ${trackPageText.length} characters`);

            const trackErrors = trackPageText.match(/(failed to load[^.]*|error[^.]*)/gi);
            if (trackErrors && trackErrors.length > 0) {
                console.log('❌ TRACK PAGE ERRORS:');
                trackErrors.forEach(msg => console.log(`   - ${msg}`));
            } else {
                console.log('✅ Track page loaded without visible errors');
            }
        } else {
            console.log('❌ No Track navigation links found');
        }
        
        // Test 3: Navigation to Earn page (Gigwork)
        console.log('💼 Testing Earn/Gigwork page...');
        const earnLinks = await page.locator('text=earn, a[href*="earn"]').count();
        console.log(`   🔗 Earn links found: ${earnLinks}`);

        if (earnLinks > 0) {
            await page.click('text=earn, a[href*="earn"]');
            await page.waitForLoadState('networkidle');
            await page.waitForTimeout(2000);

            const earnPageText = await page.locator('body').textContent();
            console.log(`   📝 Earn page text length: ${earnPageText.length} characters`);

            const earnErrors = earnPageText.match(/(failed to load[^.]*|error[^.]*)/gi);
            if (earnErrors && earnErrors.length > 0) {
                console.log('❌ EARN PAGE ERRORS:');
                earnErrors.forEach(msg => console.log(`   - ${msg}`));
            } else {
                console.log('✅ Earn page loaded without visible errors');
            }
        } else {
            console.log('❌ No Earn navigation links found');
        }
        
        // Test 4: Profile page
        console.log('👤 Testing Profile page...');
        const profileLinks = await page.locator('text=profile, a[href*="profile"], button:has-text("Profile")').count();
        console.log(`   🔗 Profile links found: ${profileLinks}`);

        if (profileLinks > 0) {
            await page.click('text=profile, a[href*="profile"], button:has-text("Profile")');
            await page.waitForLoadState('networkidle');
            await page.waitForTimeout(2000);

            const profilePageText = await page.locator('body').textContent();
            console.log(`   📝 Profile page text length: ${profilePageText.length} characters`);

            const profileErrors = profilePageText.match(/(failed to load[^.]*|error[^.]*)/gi);
            if (profileErrors && profileErrors.length > 0) {
                console.log('❌ PROFILE PAGE ERRORS:');
                profileErrors.forEach(msg => console.log(`   - ${msg}`));
            } else {
                console.log('✅ Profile page loaded without visible errors');
            }
        } else {
            console.log('❌ No Profile navigation links found');
        }
        
        // Test 5: API Health Check
        console.log('🔌 Testing API endpoints...');
        const apiTests = [
            'https://royalty.technology/.netlify/functions/users',
            'https://royalty.technology/.netlify/functions/projects',
            'https://royalty.technology/.netlify/functions/contributions'
        ];
        
        for (const apiUrl of apiTests) {
            try {
                const response = await page.request.get(apiUrl);
                console.log(`   📡 ${apiUrl}: ${response.status()}`);
            } catch (error) {
                console.log(`   ❌ ${apiUrl}: FAILED - ${error.message}`);
            }
        }
        
        // Final Summary
        console.log('\n📊 COMPREHENSIVE DIAGNOSIS SUMMARY:');
        console.log('=====================================');
        console.log(`🚨 Console Errors: ${consoleErrors.length}`);
        console.log(`🌐 Network Failures: ${networkFailures.length}`);
        console.log(`📄 Page Error Messages: ${errorMessages.length}`);
        console.log(`🎯 Interactive Elements: ${interactiveElements}`);
        
        if (consoleErrors.length > 0) {
            console.log('\n🚨 CONSOLE ERRORS:');
            consoleErrors.forEach(error => console.log(`   - ${error}`));
        }
        
        if (networkFailures.length > 0) {
            console.log('\n🌐 NETWORK FAILURES:');
            networkFailures.forEach(failure => console.log(`   - ${failure}`));
        }
        
        // Keep browser open for manual inspection
        console.log('\n⏸️ Test complete. Browser will remain open for manual inspection...');
        await page.waitForTimeout(10000); // Keep open for 10 seconds
    });
});
