import { test, expect } from '@playwright/test';

const TEST_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'TestPassword123!'
};

// Helper function to authenticate using the immersive auth flow
async function authenticate(page) {
  console.log('🔐 Attempting authentication...');
  
  await page.goto('/');
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(2000);
  
  const hasSignInButton = await page.locator('text="Sign In"').first().isVisible();
  
  if (!hasSignInButton) {
    console.log('✅ Already authenticated');
    return true;
  }
  
  console.log('📝 Need to authenticate - clicking Sign In...');
  await page.click('text="Sign In"', { timeout: 5000 });
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(2000);
  
  const hasLoginButton = await page.locator('text="LOGIN"').isVisible();
  if (!hasLoginButton) {
    throw new Error('LOGIN button not found on auth landing page');
  }
  
  console.log('📝 Clicking LOGIN button...');
  await page.click('text="LOGIN"');
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(2000);
  
  const hasEmailInput = await page.locator('input[type="email"]').isVisible();
  const hasPasswordInput = await page.locator('input[type="password"]').isVisible();
  
  if (!hasEmailInput || !hasPasswordInput) {
    throw new Error('Login form not found after clicking LOGIN');
  }
  
  console.log('📝 Filling in credentials...');
  await page.fill('input[type="email"]', TEST_CREDENTIALS.email);
  await page.fill('input[type="password"]', TEST_CREDENTIALS.password);
  
  const submitButton = page.locator('button[type="submit"]').first();
  await submitButton.click();
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(5000);
  
  const stillHasSignInButton = await page.locator('text="Sign In"').isVisible();
  if (stillHasSignInButton) {
    throw new Error('Authentication failed - Sign In button still visible');
  }
  
  console.log('✅ Authentication successful');
  return true;
}

test.describe('Quick Element Check', () => {
  test('Check for actual elements based on screenshot', async ({ page }) => {
    await authenticate(page);
    
    // Go to start page
    await page.click('text="Start"');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    console.log('🔍 Checking for elements visible in screenshot...');
    
    // Check for user dropdown (showing "the" in screenshot)
    const userDropdown = await page.locator('button:has-text("the")').isVisible();
    console.log(`User dropdown ("the"): ${userDropdown ? '✅ FOUND' : '❌ NOT FOUND'}`);
    
    // Check for project wizard elements
    const createProject = await page.locator('text="Create Your Project"').isVisible();
    console.log(`"Create Your Project" text: ${createProject ? '✅ FOUND' : '❌ NOT FOUND'}`);
    
    const traditionalWizard = await page.locator('text="Use Traditional Wizard"').isVisible();
    console.log(`"Use Traditional Wizard" button: ${traditionalWizard ? '✅ FOUND' : '❌ NOT FOUND'}`);
    
    const enhancedWizard = await page.locator('text="Enhanced Project Wizard"').isVisible();
    console.log(`"Enhanced Project Wizard" text: ${enhancedWizard ? '✅ FOUND' : '❌ NOT FOUND'}`);
    
    const guidedWizard = await page.locator('text="Follow our guided wizard"').isVisible();
    console.log(`"Follow our guided wizard" text: ${guidedWizard ? '✅ FOUND' : '❌ NOT FOUND'}`);
    
    const stepProgress = await page.locator('text="Step 1 of 7"').isVisible();
    console.log(`"Step 1 of 7" progress: ${stepProgress ? '✅ FOUND' : '❌ NOT FOUND'}`);
    
    // Check navigation buttons
    const startBtn = await page.locator('text="Start"').isVisible();
    const trackBtn = await page.locator('text="Track"').isVisible();
    const earnBtn = await page.locator('text="Earn"').isVisible();
    
    console.log(`Navigation - Start: ${startBtn ? '✅ FOUND' : '❌ NOT FOUND'}`);
    console.log(`Navigation - Track: ${trackBtn ? '✅ FOUND' : '❌ NOT FOUND'}`);
    console.log(`Navigation - Earn: ${earnBtn ? '✅ FOUND' : '❌ NOT FOUND'}`);
    
    // Take screenshot for comparison
    await page.screenshot({ path: 'test-results/quick-element-check.png', fullPage: true });
    console.log('📸 Screenshot saved for comparison');
    
    // Test if we can click the Traditional Wizard button
    if (traditionalWizard) {
      console.log('🔄 Testing Traditional Wizard button click...');
      try {
        await page.click('text="Use Traditional Wizard"');
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(2000);
        
        const currentUrl = page.url();
        console.log(`✅ Traditional Wizard clicked - URL: ${currentUrl}`);
        
        await page.screenshot({ path: 'test-results/after-wizard-click.png', fullPage: true });
      } catch (error) {
        console.log(`❌ Error clicking Traditional Wizard: ${error.message}`);
      }
    }
  });
});
