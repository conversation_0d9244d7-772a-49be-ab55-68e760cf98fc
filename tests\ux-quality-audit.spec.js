import { test, expect } from '@playwright/test';

const PRODUCTION_URL = 'https://royalty.technology';
const TEST_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'TestPassword123!'
};

// Helper function to authenticate
async function authenticate(page) {
  console.log('🔐 Authenticating...');
  await page.goto(PRODUCTION_URL);
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(2000);

  const hasLoginForm = await page.locator('input[type="email"]').isVisible();
  if (!hasLoginForm) {
    console.log('✅ Already authenticated');
    return true;
  }

  await page.fill('input[type="email"]', TEST_CREDENTIALS.email);
  await page.fill('input[type="password"]', TEST_CREDENTIALS.password);
  await page.click('text="Log In"');
  await page.waitForLoadState('networkidle');
  await page.waitForTimeout(3000);
  
  console.log('✅ Authentication completed');
  return true;
}

// Helper function to check for error messages/popups
async function checkForErrors(page, pageName) {
  const errorSelectors = [
    'text="Error"',
    'text="Failed to load"',
    'text="Something went wrong"',
    'text="Network error"',
    'text="Unable to fetch"',
    'text="Loading failed"',
    '[role="alert"]',
    '.error',
    '.alert-error',
    '.toast-error'
  ];

  const errors = [];
  for (const selector of errorSelectors) {
    const elements = await page.locator(selector).all();
    for (const element of elements) {
      if (await element.isVisible()) {
        const text = await element.textContent();
        errors.push(text);
      }
    }
  }

  if (errors.length > 0) {
    console.log(`❌ ${pageName} - Errors found:`, errors);
    return errors;
  }
  
  console.log(`✅ ${pageName} - No error messages found`);
  return [];
}

// Helper function to check for placeholder content
async function checkForPlaceholders(page, pageName) {
  const placeholderSelectors = [
    'text="Under Construction"',
    'text="Coming Soon"',
    'text="Section Under Construction"',
    'text="🚧"',
    'text="TODO"',
    'text="Lorem ipsum"',
    'text="Placeholder"',
    'text="Mock data"',
    'text="Sample"'
  ];

  const placeholders = [];
  for (const selector of placeholderSelectors) {
    const elements = await page.locator(selector).all();
    for (const element of elements) {
      if (await element.isVisible()) {
        const text = await element.textContent();
        placeholders.push(text);
      }
    }
  }

  if (placeholders.length > 0) {
    console.log(`⚠️ ${pageName} - Placeholder content found:`, placeholders);
    return placeholders;
  }
  
  console.log(`✅ ${pageName} - No placeholder content found`);
  return [];
}

// Helper function to check visual quality
async function checkVisualQuality(page, pageName) {
  const issues = [];

  // Check for broken images
  const images = await page.locator('img').all();
  for (const img of images) {
    const src = await img.getAttribute('src');
    if (src && !src.startsWith('data:')) {
      const naturalWidth = await img.evaluate(el => el.naturalWidth);
      if (naturalWidth === 0) {
        issues.push(`Broken image: ${src}`);
      }
    }
  }

  // Check for missing CSS (elements with no computed styles)
  const hasStyles = await page.evaluate(() => {
    const elements = document.querySelectorAll('*');
    let styledElements = 0;
    for (const el of elements) {
      const styles = window.getComputedStyle(el);
      if (styles.color !== 'rgb(0, 0, 0)' || styles.backgroundColor !== 'rgba(0, 0, 0, 0)') {
        styledElements++;
      }
    }
    return styledElements > 10; // Should have at least some styled elements
  });

  if (!hasStyles) {
    issues.push('Page appears to have minimal styling');
  }

  // Check for layout issues (elements overflowing)
  const overflowIssues = await page.evaluate(() => {
    const issues = [];
    const elements = document.querySelectorAll('*');
    for (const el of elements) {
      if (el.scrollWidth > el.clientWidth + 5) { // 5px tolerance
        issues.push(`Horizontal overflow in ${el.tagName.toLowerCase()}`);
      }
    }
    return issues.slice(0, 5); // Limit to first 5 issues
  });

  issues.push(...overflowIssues);

  if (issues.length > 0) {
    console.log(`⚠️ ${pageName} - Visual issues:`, issues);
    return issues;
  }

  console.log(`✅ ${pageName} - Visual quality looks good`);
  return [];
}

// Helper function to check loading performance
async function checkLoadingPerformance(page, pageName) {
  const startTime = Date.now();
  await page.waitForLoadState('networkidle');
  const loadTime = Date.now() - startTime;

  console.log(`⏱️ ${pageName} - Load time: ${loadTime}ms`);
  
  if (loadTime > 5000) {
    return [`Slow loading: ${loadTime}ms`];
  }
  
  return [];
}

test.describe('UX Quality Audit', () => {
  test('Comprehensive UX Quality Check', async ({ page }) => {
    console.log('🎨 Starting UX Quality Audit...\n');

    const qualityReport = {
      errors: {},
      placeholders: {},
      visualIssues: {},
      performanceIssues: {},
      overallScore: 0
    };

    // Authenticate first
    await authenticate(page);

    // Test each major page
    const pagesToTest = [
      { name: 'Dashboard', url: '/dashboard' },
      { name: 'Start', url: '/start' },
      { name: 'Track', url: '/track' },
      { name: 'Earn', url: '/earn' },
      { name: 'Project Create', url: '/project/create' }
    ];

    for (const pageInfo of pagesToTest) {
      console.log(`\n🔍 Testing ${pageInfo.name} page...`);
      
      await page.goto(`${PRODUCTION_URL}${pageInfo.url}`);
      
      // Check loading performance
      const perfIssues = await checkLoadingPerformance(page, pageInfo.name);
      qualityReport.performanceIssues[pageInfo.name] = perfIssues;
      
      // Check for errors
      const errors = await checkForErrors(page, pageInfo.name);
      qualityReport.errors[pageInfo.name] = errors;
      
      // Check for placeholder content
      const placeholders = await checkForPlaceholders(page, pageInfo.name);
      qualityReport.placeholders[pageInfo.name] = placeholders;
      
      // Check visual quality
      const visualIssues = await checkVisualQuality(page, pageInfo.name);
      qualityReport.visualIssues[pageInfo.name] = visualIssues;
      
      // Take screenshot for manual review
      await page.screenshot({ 
        path: `test-results/ux-quality-${pageInfo.name.toLowerCase()}.png`,
        fullPage: true 
      });
    }

    // Calculate overall quality score
    let totalIssues = 0;
    let totalPages = pagesToTest.length;
    
    Object.values(qualityReport.errors).forEach(errors => totalIssues += errors.length);
    Object.values(qualityReport.placeholders).forEach(placeholders => totalIssues += placeholders.length);
    Object.values(qualityReport.visualIssues).forEach(issues => totalIssues += issues.length);
    Object.values(qualityReport.performanceIssues).forEach(issues => totalIssues += issues.length);
    
    qualityReport.overallScore = Math.max(0, 100 - (totalIssues * 5)); // Deduct 5 points per issue

    // Print comprehensive report
    console.log('\n📊 UX QUALITY REPORT');
    console.log('====================\n');
    
    console.log('🚨 ERRORS BY PAGE:');
    Object.entries(qualityReport.errors).forEach(([page, errors]) => {
      console.log(`  ${page}: ${errors.length === 0 ? '✅ None' : `❌ ${errors.length} errors`}`);
      errors.forEach(error => console.log(`    - ${error}`));
    });
    
    console.log('\n🚧 PLACEHOLDER CONTENT:');
    Object.entries(qualityReport.placeholders).forEach(([page, placeholders]) => {
      console.log(`  ${page}: ${placeholders.length === 0 ? '✅ None' : `⚠️ ${placeholders.length} placeholders`}`);
      placeholders.forEach(placeholder => console.log(`    - ${placeholder}`));
    });
    
    console.log('\n🎨 VISUAL ISSUES:');
    Object.entries(qualityReport.visualIssues).forEach(([page, issues]) => {
      console.log(`  ${page}: ${issues.length === 0 ? '✅ Good' : `⚠️ ${issues.length} issues`}`);
      issues.forEach(issue => console.log(`    - ${issue}`));
    });
    
    console.log('\n⏱️ PERFORMANCE:');
    Object.entries(qualityReport.performanceIssues).forEach(([page, issues]) => {
      console.log(`  ${page}: ${issues.length === 0 ? '✅ Fast' : `⚠️ ${issues.length} issues`}`);
      issues.forEach(issue => console.log(`    - ${issue}`));
    });
    
    console.log(`\n🏆 OVERALL UX QUALITY SCORE: ${qualityReport.overallScore}/100`);
    
    if (qualityReport.overallScore >= 90) {
      console.log('🎉 EXCELLENT - Production ready!');
    } else if (qualityReport.overallScore >= 75) {
      console.log('👍 GOOD - Minor improvements needed');
    } else if (qualityReport.overallScore >= 60) {
      console.log('⚠️ FAIR - Several issues need attention');
    } else {
      console.log('❌ POOR - Major UX issues need fixing');
    }

    console.log('\n📸 Screenshots saved to test-results/ for manual review');
    
    // Test should pass but report issues for review
    expect(qualityReport.overallScore).toBeGreaterThan(0);
  });
});
