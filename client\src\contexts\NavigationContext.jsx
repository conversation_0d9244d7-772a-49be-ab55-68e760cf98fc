import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

/**
 * Enhanced Navigation Context
 * 
 * Provides centralized navigation state management with:
 * - Navigation history tracking
 * - User preferences persistence
 * - Mobile/desktop responsive state
 * - Accessibility features
 * - Performance optimizations
 */

// Navigation state structure
const initialState = {
  // Current navigation state
  currentView: 'grid', // 'grid', 'content'
  currentCanvas: 'home',
  zoomLevel: 0.6,
  viewPosition: { x: 0, y: 0 },

  // Input management
  inputMode: 'navigation', // 'navigation', 'content', 'editing'
  allowScroll: false,
  scrollLocked: false,

  // Navigation history
  navigationHistory: [],
  previousCanvas: null,

  // User preferences
  preferences: {
    defaultView: 'grid',
    animationsEnabled: true,
    reducedMotion: false,
    keyboardNavigationEnabled: true,
    touchGesturesEnabled: true,
    autoSavePosition: true,
    scrollSensitivity: 1.0,
  },
  
  // Device and accessibility
  isMobile: false,
  isTablet: false,
  screenSize: 'desktop',
  touchSupported: false,
  keyboardUser: false,
  
  // Performance and state
  isTransitioning: false,
  isLoading: false,
  lastInteraction: null,
  
  // Customization
  customLayout: null,
  favoriteCanvases: [],
  hiddenCanvases: [],
  
  // Analytics
  sessionStartTime: Date.now(),
  navigationCount: 0,
  totalTimeSpent: 0,
};

// Action types
const NAVIGATION_ACTIONS = {
  SET_VIEW: 'SET_VIEW',
  SET_CANVAS: 'SET_CANVAS',
  SET_ZOOM: 'SET_ZOOM',
  SET_POSITION: 'SET_POSITION',
  SET_INPUT_MODE: 'SET_INPUT_MODE',
  SET_SCROLL_STATE: 'SET_SCROLL_STATE',
  ADD_TO_HISTORY: 'ADD_TO_HISTORY',
  SET_PREFERENCES: 'SET_PREFERENCES',
  SET_DEVICE_INFO: 'SET_DEVICE_INFO',
  SET_TRANSITIONING: 'SET_TRANSITIONING',
  SET_LOADING: 'SET_LOADING',
  UPDATE_INTERACTION: 'UPDATE_INTERACTION',
  SET_CUSTOM_LAYOUT: 'SET_CUSTOM_LAYOUT',
  TOGGLE_FAVORITE: 'TOGGLE_FAVORITE',
  TOGGLE_HIDDEN: 'TOGGLE_HIDDEN',
  INCREMENT_NAVIGATION: 'INCREMENT_NAVIGATION',
  RESET_STATE: 'RESET_STATE',
};

// Navigation reducer
const navigationReducer = (state, action) => {
  switch (action.type) {
    case NAVIGATION_ACTIONS.SET_VIEW:
      return {
        ...state,
        currentView: action.payload,
        lastInteraction: Date.now(),
      };
      
    case NAVIGATION_ACTIONS.SET_CANVAS:
      return {
        ...state,
        previousCanvas: state.currentCanvas,
        currentCanvas: action.payload,
        navigationCount: state.navigationCount + 1,
        lastInteraction: Date.now(),
      };
      
    case NAVIGATION_ACTIONS.SET_ZOOM:
      return {
        ...state,
        zoomLevel: action.payload,
        lastInteraction: Date.now(),
      };
      
    case NAVIGATION_ACTIONS.SET_POSITION:
      return {
        ...state,
        viewPosition: action.payload,
        lastInteraction: Date.now(),
      };

    case NAVIGATION_ACTIONS.SET_INPUT_MODE:
      return {
        ...state,
        inputMode: action.payload,
        lastInteraction: Date.now(),
      };

    case NAVIGATION_ACTIONS.SET_SCROLL_STATE:
      return {
        ...state,
        allowScroll: action.payload.allowScroll !== undefined ? action.payload.allowScroll : state.allowScroll,
        scrollLocked: action.payload.scrollLocked !== undefined ? action.payload.scrollLocked : state.scrollLocked,
        lastInteraction: Date.now(),
      };

    case NAVIGATION_ACTIONS.ADD_TO_HISTORY:
      const newHistory = [...state.navigationHistory, action.payload].slice(-50); // Keep last 50
      return {
        ...state,
        navigationHistory: newHistory,
      };
      
    case NAVIGATION_ACTIONS.SET_PREFERENCES:
      return {
        ...state,
        preferences: { ...state.preferences, ...action.payload },
      };
      
    case NAVIGATION_ACTIONS.SET_DEVICE_INFO:
      return {
        ...state,
        ...action.payload,
      };
      
    case NAVIGATION_ACTIONS.SET_TRANSITIONING:
      return {
        ...state,
        isTransitioning: action.payload,
      };
      
    case NAVIGATION_ACTIONS.SET_LOADING:
      return {
        ...state,
        isLoading: action.payload,
      };
      
    case NAVIGATION_ACTIONS.UPDATE_INTERACTION:
      return {
        ...state,
        lastInteraction: Date.now(),
        totalTimeSpent: Date.now() - state.sessionStartTime,
      };
      
    case NAVIGATION_ACTIONS.SET_CUSTOM_LAYOUT:
      return {
        ...state,
        customLayout: action.payload,
      };
      
    case NAVIGATION_ACTIONS.TOGGLE_FAVORITE:
      const favorites = state.favoriteCanvases.includes(action.payload)
        ? state.favoriteCanvases.filter(id => id !== action.payload)
        : [...state.favoriteCanvases, action.payload];
      return {
        ...state,
        favoriteCanvases: favorites,
      };
      
    case NAVIGATION_ACTIONS.TOGGLE_HIDDEN:
      const hidden = state.hiddenCanvases.includes(action.payload)
        ? state.hiddenCanvases.filter(id => id !== action.payload)
        : [...state.hiddenCanvases, action.payload];
      return {
        ...state,
        hiddenCanvases: hidden,
      };
      
    case NAVIGATION_ACTIONS.INCREMENT_NAVIGATION:
      return {
        ...state,
        navigationCount: state.navigationCount + 1,
      };
      
    case NAVIGATION_ACTIONS.RESET_STATE:
      return {
        ...initialState,
        sessionStartTime: Date.now(),
      };
      
    default:
      return state;
  }
};

// Create context
const NavigationContext = createContext();

// Navigation provider component
export const NavigationProvider = ({ children, currentUser }) => {
  const [state, dispatch] = useReducer(navigationReducer, initialState);
  const location = useLocation();
  const navigate = useNavigate();

  // Device detection
  useEffect(() => {
    const updateDeviceInfo = () => {
      const width = window.innerWidth;
      const isMobile = width < 768;
      const isTablet = width >= 768 && width < 1024;
      const touchSupported = 'ontouchstart' in window;
      
      let screenSize = 'desktop';
      if (isMobile) screenSize = 'mobile';
      else if (isTablet) screenSize = 'tablet';
      
      dispatch({
        type: NAVIGATION_ACTIONS.SET_DEVICE_INFO,
        payload: {
          isMobile,
          isTablet,
          screenSize,
          touchSupported,
        },
      });
    };

    updateDeviceInfo();
    window.addEventListener('resize', updateDeviceInfo);
    return () => window.removeEventListener('resize', updateDeviceInfo);
  }, []);

  // Load user preferences
  useEffect(() => {
    if (currentUser) {
      const savedPrefs = localStorage.getItem(`nav_preferences_${currentUser.id}`);
      if (savedPrefs) {
        try {
          const preferences = JSON.parse(savedPrefs);
          dispatch({
            type: NAVIGATION_ACTIONS.SET_PREFERENCES,
            payload: preferences,
          });
        } catch (error) {
          console.warn('Failed to load navigation preferences:', error);
        }
      }
    }
  }, [currentUser]);

  // Save preferences when they change
  useEffect(() => {
    if (currentUser) {
      localStorage.setItem(
        `nav_preferences_${currentUser.id}`,
        JSON.stringify(state.preferences)
      );
    }
  }, [state.preferences, currentUser]);

  // Detect reduced motion preference
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    const handleChange = () => {
      dispatch({
        type: NAVIGATION_ACTIONS.SET_PREFERENCES,
        payload: { reducedMotion: mediaQuery.matches },
      });
    };

    handleChange();
    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  // Navigation actions
  const actions = {
    setView: (view) => dispatch({ type: NAVIGATION_ACTIONS.SET_VIEW, payload: view }),
    setCanvas: (canvas) => dispatch({ type: NAVIGATION_ACTIONS.SET_CANVAS, payload: canvas }),
    setZoom: (zoom) => dispatch({ type: NAVIGATION_ACTIONS.SET_ZOOM, payload: zoom }),
    setPosition: (position) => dispatch({ type: NAVIGATION_ACTIONS.SET_POSITION, payload: position }),

    setInputMode: (mode) => dispatch({ type: NAVIGATION_ACTIONS.SET_INPUT_MODE, payload: mode }),
    setScrollState: (scrollState) => dispatch({ type: NAVIGATION_ACTIONS.SET_SCROLL_STATE, payload: scrollState }),

    addToHistory: (entry) => dispatch({ type: NAVIGATION_ACTIONS.ADD_TO_HISTORY, payload: entry }),
    
    updatePreferences: (prefs) => dispatch({ type: NAVIGATION_ACTIONS.SET_PREFERENCES, payload: prefs }),
    
    setTransitioning: (transitioning) => dispatch({ type: NAVIGATION_ACTIONS.SET_TRANSITIONING, payload: transitioning }),
    setLoading: (loading) => dispatch({ type: NAVIGATION_ACTIONS.SET_LOADING, payload: loading }),
    
    updateInteraction: () => dispatch({ type: NAVIGATION_ACTIONS.UPDATE_INTERACTION }),
    
    setCustomLayout: (layout) => dispatch({ type: NAVIGATION_ACTIONS.SET_CUSTOM_LAYOUT, payload: layout }),
    toggleFavorite: (canvasId) => dispatch({ type: NAVIGATION_ACTIONS.TOGGLE_FAVORITE, payload: canvasId }),
    toggleHidden: (canvasId) => dispatch({ type: NAVIGATION_ACTIONS.TOGGLE_HIDDEN, payload: canvasId }),
    
    incrementNavigation: () => dispatch({ type: NAVIGATION_ACTIONS.INCREMENT_NAVIGATION }),
    
    resetState: () => dispatch({ type: NAVIGATION_ACTIONS.RESET_STATE }),
    
    // Navigation helpers
    navigateToCanvas: (canvasId, options = {}) => {
      actions.addToHistory({
        from: state.currentCanvas,
        to: canvasId,
        timestamp: Date.now(),
        method: options.method || 'click',
        view: state.currentView,
      });

      actions.setCanvas(canvasId);
      actions.incrementNavigation();

      if (options.route || options.resolvedRoute) {
        navigate(options.resolvedRoute || options.route);
      }
    },
    
    goBack: () => {
      if (state.previousCanvas) {
        actions.setCanvas(state.previousCanvas);
      } else if (state.navigationHistory.length > 0) {
        const lastEntry = state.navigationHistory[state.navigationHistory.length - 1];
        actions.setCanvas(lastEntry.from);
      }
    },
    
    // Analytics helpers
    getSessionStats: () => ({
      sessionDuration: Date.now() - state.sessionStartTime,
      navigationCount: state.navigationCount,
      averageTimePerNavigation: state.navigationCount > 0 
        ? (Date.now() - state.sessionStartTime) / state.navigationCount 
        : 0,
      mostVisitedCanvas: state.navigationHistory.reduce((acc, entry) => {
        acc[entry.to] = (acc[entry.to] || 0) + 1;
        return acc;
      }, {}),
    }),
  };

  const value = {
    ...state,
    actions,
  };

  return (
    <NavigationContext.Provider value={value}>
      {children}
    </NavigationContext.Provider>
  );
};

// Hook to use navigation context
export const useNavigation = () => {
  const context = useContext(NavigationContext);
  if (!context) {
    throw new Error('useNavigation must be used within a NavigationProvider');
  }
  return context;
};

export default NavigationContext;
