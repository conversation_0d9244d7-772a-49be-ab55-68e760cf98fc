import { createClient } from '@supabase/supabase-js';

// Use the actual Supabase credentials
const supabaseUrl = 'https://hqqlrrqvjcetoxbdjgzx.supabase.co';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhxcWxycnF2amNldG94YmRqZ3p4Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MzgyMDU1OSwiZXhwIjoyMDU5Mzk2NTU5fQ.k5xvFBzBxNMCtPd0d8Ko9dAcT5Y3op3ZgFvlAf85LLs';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function addTestCollaborationRequests() {
  console.log('🚀 Adding test collaboration requests...');

  try {
    // First, get a test user to use as the requester
    // Try to get from profiles table first
    let testUser = null;

    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('id, user_id, display_name')
      .limit(1);

    if (profiles && profiles.length > 0) {
      testUser = { id: profiles[0].user_id, display_name: profiles[0].display_name };
      console.log(`📋 Using test user from profiles: ${testUser.display_name || testUser.id} (${testUser.id})`);
    } else {
      // Fallback: use a known test user ID
      testUser = { id: '93cbbbed-2772-4922-b7d7-d07fdc1aa62b', display_name: 'Test User' };
      console.log(`📋 Using fallback test user: ${testUser.display_name} (${testUser.id})`);
    }

    // Sample collaboration requests
    const testRequests = [
      {
        requester_id: testUser.id,
        project_title: 'Frontend Developer for React Dashboard',
        project_description: 'Looking for an experienced React developer to help build a modern dashboard interface. The project involves creating responsive components, integrating with APIs, and implementing user authentication.',
        required_skills: ['React', 'JavaScript', 'CSS', 'API Integration'],
        budget_range_min: 2000,
        budget_range_max: 5000,
        timeline_weeks: 6,
        project_type: 'fixed',
        experience_level: 'intermediate',
        target_audience: 'public',
        status: 'open'
      },
      {
        requester_id: testUser.id,
        project_title: 'UI/UX Designer for Mobile App',
        project_description: 'Seeking a talented UI/UX designer to create wireframes and high-fidelity designs for a mobile application. Experience with Figma and mobile design patterns required.',
        required_skills: ['UI/UX Design', 'Figma', 'Mobile Design', 'Prototyping'],
        budget_range_min: 1500,
        budget_range_max: 3500,
        timeline_weeks: 4,
        project_type: 'fixed',
        experience_level: 'advanced',
        target_audience: 'public',
        status: 'open'
      },
      {
        requester_id: testUser.id,
        project_title: 'Backend API Development',
        project_description: 'Need a backend developer to create RESTful APIs for a web application. Must have experience with Node.js, Express, and database design.',
        required_skills: ['Node.js', 'Express', 'PostgreSQL', 'API Design'],
        budget_range_min: 3000,
        budget_range_max: 7000,
        timeline_weeks: 8,
        project_type: 'fixed',
        experience_level: 'advanced',
        target_audience: 'public',
        status: 'open'
      },
      {
        requester_id: testUser.id,
        project_title: 'Content Writer for Tech Blog',
        project_description: 'Looking for a technical content writer to create engaging blog posts about software development, AI, and emerging technologies.',
        required_skills: ['Technical Writing', 'SEO', 'Content Strategy', 'Research'],
        budget_range_min: 800,
        budget_range_max: 2000,
        timeline_weeks: 3,
        project_type: 'ongoing',
        experience_level: 'intermediate',
        target_audience: 'public',
        status: 'open'
      },
      {
        requester_id: testUser.id,
        project_title: 'DevOps Engineer for CI/CD Setup',
        project_description: 'Seeking a DevOps engineer to set up continuous integration and deployment pipelines. Experience with Docker, AWS, and GitHub Actions required.',
        required_skills: ['DevOps', 'Docker', 'AWS', 'CI/CD', 'GitHub Actions'],
        budget_range_min: 2500,
        budget_range_max: 6000,
        timeline_weeks: 5,
        project_type: 'fixed',
        experience_level: 'expert',
        target_audience: 'public',
        status: 'open'
      }
    ];

    // Insert the test requests using the service role client to bypass RLS
    const { data: insertedRequests, error: insertError } = await supabase
      .from('collaboration_requests')
      .insert(testRequests)
      .select();

    if (insertError) {
      console.error('Error inserting collaboration requests:', insertError);
      return;
    }

    console.log(`✅ Successfully added ${insertedRequests.length} test collaboration requests:`);
    
    insertedRequests.forEach((request, index) => {
      console.log(`   ${index + 1}. ${request.project_title}`);
      console.log(`      Budget: $${request.budget_range_min} - $${request.budget_range_max}`);
      console.log(`      Timeline: ${request.timeline_weeks} weeks`);
      console.log(`      Type: ${request.project_type}`);
      console.log(`      Experience: ${request.experience_level}`);
      console.log('');
    });

    // Verify the requests were created
    const { data: verifyRequests, error: verifyError } = await supabase
      .from('collaboration_requests')
      .select('id, project_title, status')
      .eq('requester_id', testUser.id);

    if (verifyError) {
      console.error('Error verifying requests:', verifyError);
    } else {
      console.log(`🔍 Verification: Found ${verifyRequests.length} collaboration requests for user ${testUser.display_name || testUser.id}`);
    }

    console.log('✅ Test collaboration requests added successfully!');
    console.log('🎯 You can now test the Apply for Gigwork functionality');

  } catch (error) {
    console.error('❌ Error adding test collaboration requests:', error);
  }
}

// Run the script
addTestCollaborationRequests()
  .then(() => {
    console.log('🏁 Script completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Script failed:', error);
    process.exit(1);
  });
