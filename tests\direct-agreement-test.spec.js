import { test, expect } from '@playwright/test';

test.describe('Direct Agreement Test', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to login page and authenticate
    await page.goto('https://royalty.technology/login');
    await page.waitForLoadState('networkidle');
    
    // Login with test credentials
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'TestPassword123!');
    await page.click('button[type="submit"]');
    
    // Wait for authentication to complete
    await page.waitForURL('**/dashboard', { timeout: 10000 });
    await page.waitForLoadState('networkidle');
  });

  test('Test agreement generation directly in browser', async ({ page }) => {
    console.log('🔍 Testing agreement generation directly...');

    // Navigate to project creation
    await page.goto('https://royalty.technology/project/create');
    await page.waitForLoadState('networkidle');
    
    // Navigate through wizard steps to reach Review & Agreement
    await page.fill('input[placeholder="Enter project name"]', 'Direct Test Project');
    await page.fill('textarea[placeholder="Describe your project"]', 'Test project for direct testing');
    
    for (let i = 0; i < 6; i++) {
      await page.waitForTimeout(1000);
      const nextBtn = page.locator('button:has-text("Next"), button:has-text("Continue")').first();
      if (await nextBtn.isVisible()) {
        await nextBtn.click({ force: true });
        await page.waitForTimeout(1500);
      }
    }
    
    // Wait for Review & Agreement page
    await page.waitForTimeout(3000);
    const reviewTitle = page.locator('h2:has-text("Review & Agreement")');
    await expect(reviewTitle).toBeVisible({ timeout: 10000 });
    console.log('✓ Reached Review & Agreement step');

    // Execute agreement generation directly in the browser console
    const result = await page.evaluate(async () => {
      try {
        // Import the agreement generation function
        const { generateAgreement } = await import('/src/utils/agreement/index.js');
        
        // Create test data
        const testProjectData = {
          name: 'Direct Test Project',
          description: 'Test project for direct testing',
          company_name: 'Test Company Inc.',
          company_address: '123 Test Street, Test City, Test State 12345',
          company_state: 'Test State',
          company_city: 'Test City',
          contact_email: '<EMAIL>',
          project_type: 'software'
        };

        const testContributors = [{
          permission_level: 'Owner',
          display_name: 'Test Owner',
          email: '<EMAIL>',
          address: '123 Test Street, Test City, Test State 12345',
          state: 'Test State',
          city: 'Test City',
          title: 'Project Owner',
          users: {
            display_name: 'Test Owner',
            email: '<EMAIL>'
          }
        }];

        const testRoyaltyModel = {
          model_type: 'equal',
          revenue_share: 50
        };

        // Generate agreement
        const agreement = await generateAgreement(testProjectData, testContributors, testRoyaltyModel);
        
        return {
          success: true,
          agreementLength: agreement?.length || 0,
          agreementPreview: agreement?.substring(0, 500) || 'No content',
          error: null
        };
      } catch (error) {
        return {
          success: false,
          agreementLength: 0,
          agreementPreview: 'Error occurred',
          error: error.message
        };
      }
    });

    console.log('📊 Direct Agreement Generation Result:');
    console.log('✅ Success:', result.success);
    console.log('📄 Agreement Length:', result.agreementLength);
    console.log('📄 Agreement Preview:', result.agreementPreview);
    if (result.error) {
      console.log('❌ Error:', result.error);
    }

    // Take a screenshot
    await page.screenshot({ path: 'direct-agreement-test.png', fullPage: true });
    console.log('📸 Screenshot saved as direct-agreement-test.png');

    // Verify the result
    expect(result.success).toBe(true);
    expect(result.agreementLength).toBeGreaterThan(1000);
  });
});
