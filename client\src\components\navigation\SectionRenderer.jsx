import React, { Suspense, lazy } from 'react';
import { motion } from 'framer-motion';
import { Card, CardBody } from '@heroui/react';
import SimpleLoading from '../layout/SimpleLoading';

/**
 * Section Renderer Component
 *
 * Dynamically loads and renders section components based on canvas and section configuration.
 * Provides lazy loading and error boundaries for section components.
 */

// Helper function to safely load components
const safeImport = (importPath) => {
  return lazy(() =>
    import(importPath).catch(() => ({
      default: (props) => <PlaceholderSection {...props} componentName={importPath} />
    }))
  );
};

// Lazy load section components
const sectionComponents = {
  // Home canvas sections (these exist)
  DashboardOverview: lazy(() => import('../../sections/home/<USER>')),
  RecentActivity: lazy(() => import('../../sections/home/<USER>')),
  QuickActions: lazy(() => import('../../sections/home/<USER>')),
  NotificationCenter: lazy(() => import('../../sections/home/<USER>')),

  // Teams canvas sections
  MyTeams: lazy(() => import('../../sections/teams/MyTeams')),
  TeamInvitations: lazy(() => import('../../sections/teams/TeamInvitations')),
  TeamCreation: lazy(() => import('../../sections/teams/TeamCreation')),
  CollaborationTools: lazy(() => import('../../sections/teams/CollaborationTools')),

  // Revenue sections
  RevenueDashboard: lazy(() => import('../../sections/revenue/RevenueDashboard')),
  PaymentHistory: lazy(() => import('../../sections/revenue/PaymentHistory')),
  RevenueProjections: safeImport('../../sections/revenue/RevenueProjections'),
  FinancialReports: safeImport('../../sections/revenue/FinancialReports'),

  // Analytics sections
  ContributionAnalytics: safeImport('../../sections/analytics/ContributionAnalytics'),
  ProjectMetrics: safeImport('../../sections/analytics/ProjectMetrics'),
  TeamPerformance: safeImport('../../sections/analytics/TeamPerformance'),
  TrendAnalysis: safeImport('../../sections/analytics/TrendAnalysis'),

  // Validation sections
  PendingReview: lazy(() => import('../../sections/validation/PendingReview')),
  ValidationMetrics: lazy(() => import('../../sections/validation/ValidationMetrics')),
  ApprovalWorkflow: lazy(() => import('../../sections/validation/ApprovalWorkflow')),
  FeedbackSystem: lazy(() => import('../../sections/validation/FeedbackSystem')),

  // Learning sections
  InteractiveTutorials: lazy(() => import('../../sections/learning/InteractiveTutorials')),
  Documentation: lazy(() => import('../../sections/learning/Documentation')),
  BestPractices: lazy(() => import('../../sections/learning/BestPractices')),
  SkillCertification: lazy(() => import('../../sections/learning/SkillCertification')),

  // Project sections
  ProjectList: safeImport('../../sections/projects/ProjectList'),
  ProjectDetail: lazy(() => import('../../sections/projects/ProjectDetail')),
  TeamManagement: lazy(() => import('../../sections/projects/TeamManagement')),

  // Contribution sections
  TimeTracker: safeImport('../../sections/contributions/TimeTracker'),
  ContributionLog: safeImport('../../sections/contributions/ContributionLog'),
  DifficultyAssessment: lazy(() => import('../../sections/contributions/DifficultyAssessment')),

  // Bug sections
  BugReportForm: lazy(() => import('../../sections/bugs/BugReportForm')),
  MyBugReports: lazy(() => import('../../sections/bugs/MyBugReports')),
  BugStatusTracker: lazy(() => import('../../sections/bugs/BugStatusTracker')),
  GeneralFeedback: lazy(() => import('../../sections/bugs/GeneralFeedback')),
};

// Placeholder component for sections that don't exist yet - DEPRECATED
// This component should no longer be used as all sections should have real implementations
const PlaceholderSection = ({ componentName, sectionId, canvasId }) => (
  <div className="p-6">
    <Card className="bg-white/10 backdrop-blur-md border-white/20">
      <CardBody className="p-8 text-center">
        <span className="text-6xl mb-4 block">⚠️</span>
        <h2 className="text-2xl font-bold text-white mb-4">Missing Section Implementation</h2>
        <p className="text-white/70 mb-6">
          The <strong>{componentName}</strong> section needs to be implemented.
        </p>
        <div className="bg-white/5 rounded-lg p-4 text-left">
          <h3 className="text-white font-medium mb-2">Development Info:</h3>
          <div className="text-white/60 text-sm space-y-1">
            <p>Canvas: {canvasId}</p>
            <p>Section: {sectionId}</p>
            <p>Component: {componentName}</p>
            <p className="text-red-400">This should be replaced with a real component</p>
          </div>
        </div>
      </CardBody>
    </Card>
  </div>
);

// Loading component for sections
const SectionLoading = ({ sectionId }) => (
  <div className="flex items-center justify-center h-64">
    <Card className="bg-white/10 backdrop-blur-md">
      <CardBody className="p-8 text-center">
        <SimpleLoading text={`Loading ${sectionId} section...`} />
      </CardBody>
    </Card>
  </div>
);

// Error boundary component
class SectionErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('Section rendering error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="p-6">
          <Card className="bg-red-500/10 backdrop-blur-md border-red-500/20">
            <CardBody className="p-8 text-center">
              <span className="text-6xl mb-4 block">⚠️</span>
              <h2 className="text-2xl font-bold text-white mb-4">Section Error</h2>
              <p className="text-white/70 mb-4">
                There was an error loading this section.
              </p>
              <div className="bg-white/5 rounded-lg p-4 text-left">
                <h3 className="text-white font-medium mb-2">Error Details:</h3>
                <code className="text-red-300 text-sm">
                  {this.state.error?.message || 'Unknown error'}
                </code>
              </div>
            </CardBody>
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
}

const SectionRenderer = ({
  canvasId,
  sectionId,
  componentName,
  props = {}
}) => {
  // Get the component to render, or use placeholder if not found
  const Component = sectionComponents[componentName] || (() => (
    <PlaceholderSection
      componentName={componentName}
      sectionId={sectionId}
      canvasId={canvasId}
    />
  ));

  return (
    <motion.div
      key={`${canvasId}-${sectionId}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
      className="h-full"
    >
      <SectionErrorBoundary>
        <Suspense fallback={<SectionLoading sectionId={sectionId} />}>
          <Component
            canvasId={canvasId}
            sectionId={sectionId}
            {...props}
          />
        </Suspense>
      </SectionErrorBoundary>
    </motion.div>
  );
};

export default SectionRenderer;
