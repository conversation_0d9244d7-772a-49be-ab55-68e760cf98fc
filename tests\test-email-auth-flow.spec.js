import { test, expect } from '@playwright/test';

test.describe('Email Authentication and Project Creation Flow', () => {
  test('Direct email login and company fields testing', async ({ page }) => {
    console.log('🚀 Testing direct email authentication and project creation...');

    // Set up JavaScript error tracking and console monitoring
    const jsErrors = [];
    const consoleMessages = [];
    page.on('console', msg => {
      const message = `${msg.type()}: ${msg.text()}`;
      consoleMessages.push(message);

      // Log ALL console messages to see what's happening
      console.log(`🔍 Console [${msg.type()}]: ${msg.text()}`);

      if (msg.type() === 'error') {
        jsErrors.push(msg.text());
      }
    });

    // Step 1: Direct navigation to login page
    console.log('📍 Step 1: Direct Login Navigation');
    await page.goto('https://royalty.technology/login');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    await page.screenshot({ path: 'test-results/email-01-login-page.png', fullPage: true });
    console.log(`🌐 Login page URL: ${page.url()}`);
    
    // Step 2: Look for email/password form (avoid OAuth)
    console.log('📍 Step 2: Email/Password Authentication');
    
    // Wait for login form elements
    await page.waitForTimeout(2000);
    
    // Look for email input with multiple selectors
    const emailSelectors = [
      'input[type="email"]',
      'input[placeholder*="email" i]',
      'input[name="email"]',
      'input[id*="email"]',
      '[data-testid*="email"]'
    ];
    
    const passwordSelectors = [
      'input[type="password"]',
      'input[placeholder*="password" i]',
      'input[name="password"]',
      'input[id*="password"]',
      '[data-testid*="password"]'
    ];
    
    let emailInput = null;
    let passwordInput = null;
    
    // Find email input
    for (const selector of emailSelectors) {
      const element = page.locator(selector).first();
      if (await element.isVisible({ timeout: 1000 })) {
        emailInput = element;
        console.log(`✅ Found email input with selector: ${selector}`);
        break;
      }
    }
    
    // Find password input
    for (const selector of passwordSelectors) {
      const element = page.locator(selector).first();
      if (await element.isVisible({ timeout: 1000 })) {
        passwordInput = element;
        console.log(`✅ Found password input with selector: ${selector}`);
        break;
      }
    }
    
    if (!emailInput || !passwordInput) {
      console.log('❌ Could not find email/password form');
      console.log('🔍 Looking for alternative login methods...');
      
      // Check what's actually on the page
      const pageContent = await page.content();
      const hasEmailForm = pageContent.includes('email') || pageContent.includes('Email');
      const hasPasswordForm = pageContent.includes('password') || pageContent.includes('Password');
      const hasOAuthOnly = pageContent.includes('Google') || pageContent.includes('GitHub');
      
      console.log(`📄 Page has email form: ${hasEmailForm}`);
      console.log(`📄 Page has password form: ${hasPasswordForm}`);
      console.log(`📄 Page has OAuth only: ${hasOAuthOnly}`);
      
      // Try to find a "Sign in with email" or similar button
      const emailLoginButton = page.locator('button:has-text("email"), button:has-text("Email"), a:has-text("email"), a:has-text("Email")').first();
      if (await emailLoginButton.isVisible()) {
        console.log('✅ Found email login button, clicking...');
        await emailLoginButton.click();
        await page.waitForTimeout(2000);
        
        // Try to find email/password inputs again
        emailInput = page.locator('input[type="email"]').first();
        passwordInput = page.locator('input[type="password"]').first();
      }
    }
    
    // Step 3: Perform authentication if form is available
    if (emailInput && passwordInput && await emailInput.isVisible() && await passwordInput.isVisible()) {
      console.log('📝 Filling authentication credentials...');
      
      await emailInput.fill('<EMAIL>');
      await passwordInput.fill('TestPassword123!');
      
      await page.screenshot({ path: 'test-results/email-02-credentials-filled.png', fullPage: true });
      
      // Find and click submit button
      const submitSelectors = [
        'button[type="submit"]',
        'button:has-text("Login")',
        'button:has-text("Sign In")',
        'button:has-text("Submit")',
        'input[type="submit"]'
      ];
      
      let submitButton = null;
      for (const selector of submitSelectors) {
        const element = page.locator(selector).first();
        if (await element.isVisible({ timeout: 1000 })) {
          submitButton = element;
          console.log(`✅ Found submit button with selector: ${selector}`);
          break;
        }
      }
      
      if (submitButton) {
        console.log('🔐 Submitting login form...');
        await submitButton.click();
        await page.waitForTimeout(8000); // Wait for authentication
        
        await page.screenshot({ path: 'test-results/email-03-after-login.png', fullPage: true });
        console.log(`🌐 URL after login: ${page.url()}`);
        
        // Check if login was successful
        const stillOnLogin = await page.locator('input[type="email"]').isVisible({ timeout: 2000 });
        if (stillOnLogin) {
          console.log('❌ Login may have failed, still on login page');
        } else {
          console.log('✅ Login appears successful');
        }
      } else {
        console.log('❌ Could not find submit button');
      }
    } else {
      console.log('❌ Email/password form not available');
      console.log('🔄 Attempting to bypass authentication for testing...');
      
      // Try direct navigation to authenticated areas
      const testUrls = [
        'https://royalty.technology/dashboard',
        'https://royalty.technology/projects',
        'https://royalty.technology/start'
      ];
      
      for (const url of testUrls) {
        console.log(`🔄 Trying: ${url}`);
        await page.goto(url);
        await page.waitForTimeout(3000);
        
        const notOnLogin = !page.url().includes('/login') && !page.url().includes('google.com');
        if (notOnLogin) {
          console.log(`✅ Successfully accessed: ${url}`);
          break;
        }
      }
    }
    
    // Step 4: Navigate to project creation
    console.log('📍 Step 4: Navigate to Project Creation');
    
    // Try multiple navigation approaches
    const navigationAttempts = [
      { type: 'button', selector: 'button:has-text("Start")' },
      { type: 'button', selector: 'button:has-text("Create Project")' },
      { type: 'button', selector: 'button:has-text("New Project")' },
      { type: 'link', selector: 'a:has-text("Start")' },
      { type: 'link', selector: 'a:has-text("Projects")' },
      { type: 'direct', url: 'https://royalty.technology/project/create' },
      { type: 'direct', url: 'https://royalty.technology/projects/new' }
    ];
    
    let projectPageFound = false;
    
    for (const attempt of navigationAttempts) {
      if (attempt.type === 'direct') {
        console.log(`🔄 Direct navigation to: ${attempt.url}`);
        await page.goto(attempt.url);
        await page.waitForTimeout(3000);
      } else {
        const element = page.locator(attempt.selector).first();
        if (await element.isVisible({ timeout: 2000 })) {
          console.log(`✅ Found ${attempt.type}: ${attempt.selector}`);
          await element.click();
          await page.waitForTimeout(3000);
        } else {
          continue;
        }
      }
      
      // Check if we're on a project creation page
      const hasProjectElements = await page.locator('h1:has-text("Create Your Project")').isVisible({ timeout: 2000 });
      if (hasProjectElements) {
        console.log(`✅ Found project creation page via: ${attempt.type} - ${attempt.selector || attempt.url}`);
        projectPageFound = true;
        break;
      }
    }
    
    await page.screenshot({ path: 'test-results/email-04-project-page.png', fullPage: true });
    console.log(`🌐 Current URL: ${page.url()}`);
    
    // Step 5: Test Company Information Fields
    console.log('📍 Step 5: Company Information Fields Testing');

    // First, scroll down to make sure we can see all content
    await page.evaluate(() => window.scrollTo(0, document.body.scrollHeight));
    await page.waitForTimeout(1000);

    // Take a screenshot after scrolling
    await page.screenshot({ path: 'test-results/email-04b-after-scroll.png', fullPage: true });

    // Look for company information section with multiple approaches
    console.log('🔍 Looking for Company Information section...');

    // Check if the text exists anywhere on the page
    const pageContent = await page.content();
    const hasCompanyText = pageContent.includes('Company Information');
    console.log(`📄 Page contains "Company Information" text: ${hasCompanyText}`);

    // Try multiple selectors for the company section
    const companySectionSelectors = [
      'h3:has-text("Company Information")',
      'text="Company Information"',
      '[class*="company"]',
      'div:has-text("Company Information")',
      'span:has-text("Company Information")'
    ];

    let companyVisible = false;
    for (const selector of companySectionSelectors) {
      const element = page.locator(selector).first();
      if (await element.isVisible({ timeout: 2000 })) {
        console.log(`✅ Found Company Information with selector: ${selector}`);
        companyVisible = true;
        break;
      }
    }

    console.log(`✅ Company Information section visible: ${companyVisible}`);

    // JavaScript errors are already being tracked from the beginning of the test

    // Debug: Get all input elements on the page
    const allInputs = await page.locator('input').all();
    console.log(`🔍 Total input elements found: ${allInputs.length}`);

    for (let i = 0; i < Math.min(allInputs.length, 10); i++) {
      const input = allInputs[i];
      const placeholder = await input.getAttribute('placeholder');
      const type = await input.getAttribute('type');
      const name = await input.getAttribute('name');
      const dataSlot = await input.getAttribute('data-slot');
      console.log(`  Input ${i + 1}: placeholder="${placeholder}", type="${type}", name="${name}", data-slot="${dataSlot}"`);
    }

    // Debug: Check for company information div structure
    const companyDivs = await page.locator('div:has-text("Company Information")').all();
    console.log(`🏢 Company Information divs found: ${companyDivs.length}`);

    // Debug: Look for the specific company information container (corrected selector)
    const companyContainer = page.locator('div:has-text("Company Information")').first();
    const containerExists = await companyContainer.isVisible({ timeout: 2000 });
    console.log(`📦 Company container visible: ${containerExists}`);

    // Debug: Check for HeroUI/NextUI specific elements
    const heroUIElements = await page.locator('[data-slot], [class*="nextui"], [class*="heroui"]').all();
    console.log(`⚡ HeroUI/NextUI elements found: ${heroUIElements.length}`);

    // Debug: Check for debug elements
    const debugDiv = page.locator('div:has-text("🔍 DEBUG: This should be visible if rendering works")');
    const debugVisible = await debugDiv.isVisible().catch(() => false);
    console.log(`🔍 Debug div visible: ${debugVisible}`);

    // Debug: Check for specific company input fields by label and placeholder
    const companyInputs = [
      { label: 'Company Name', placeholder: 'Enter company name' },
      { label: 'Company Address', placeholder: 'Enter company address' },
      { label: 'City', placeholder: 'Enter city' },
      { label: 'Contact Email', placeholder: 'Enter contact email' },
      { label: 'Authorized Signer Name', placeholder: 'Enter name of person authorized' }
    ];

    for (const field of companyInputs) {
      const inputByPlaceholder = page.locator(`input[placeholder="${field.placeholder}"]`);
      const inputByAriaLabel = page.locator(`input[aria-label="${field.label}"]`);
      const placeholderVisible = await inputByPlaceholder.isVisible().catch(() => false);
      const ariaLabelVisible = await inputByAriaLabel.isVisible().catch(() => false);
      console.log(`📝 ${field.label} input (placeholder): ${placeholderVisible}`);
      console.log(`📝 ${field.label} input (aria-label): ${ariaLabelVisible}`);
    }

    if (containerExists) {
      const containerHTML = await companyContainer.innerHTML();
      console.log(`📄 Company container HTML length: ${containerHTML.length} characters`);

      // Look for Input components within the container
      const inputsInContainer = await companyContainer.locator('input').all();
      console.log(`🔍 Inputs within company container: ${inputsInContainer.length}`);

      // Debug: Check for HeroUI Input components specifically
      const heroInputsInContainer = await companyContainer.locator('[data-slot="input"]').all();
      console.log(`⚡ HeroUI inputs in container: ${heroInputsInContainer.length}`);

      // Debug: Check for any div with space-y-2 class (our input wrappers)
      const inputWrappers = await companyContainer.locator('div.space-y-2').all();
      console.log(`📦 Input wrapper divs: ${inputWrappers.length}`);

      // Debug: Look for specific labels
      const labels = await companyContainer.locator('label, [data-slot="label"]').all();
      console.log(`🏷️ Labels in container: ${labels.length}`);

      // Debug: Look for specific company field labels
      const companyLabels = ['Company Name', 'Company Address', 'City', 'Contact Email', 'Authorized Signer Name'];
      for (const labelText of companyLabels) {
        const labelElement = companyContainer.locator(`label:has-text("${labelText}"), [data-slot="label"]:has-text("${labelText}")`);
        const labelExists = await labelElement.count();
        console.log(`🏷️ "${labelText}" label found: ${labelExists}`);

        if (labelExists > 0) {
          const labelVisible = await labelElement.isVisible();
          console.log(`👁️ "${labelText}" label visible: ${labelVisible}`);
        }
      }

      // Debug: Print a sample of the HTML content
      console.log(`📄 Company container HTML sample: ${containerHTML.substring(0, 500)}...`);
    } else {
      console.log(`❌ Company container not found or not visible`);

      // Debug: Check if any company-related text exists at all
      const companyText = await page.locator('text="Company Information"').all();
      console.log(`📝 "Company Information" text elements: ${companyText.length}`);

      // Debug: Check for any border-gray-200 divs
      const borderDivs = await page.locator('div.border.border-gray-200').all();
      console.log(`🔲 Border gray divs found: ${borderDivs.length}`);
    }

    // Check for any JavaScript errors
    if (jsErrors.length > 0) {
      console.log(`❌ JavaScript errors detected:`);
      jsErrors.forEach((error, index) => {
        console.log(`  Error ${index + 1}: ${error}`);
      });
    } else {
      console.log(`✅ No JavaScript errors detected`);
    }

    // Check for our debug console.log messages
    const debugMessages = consoleMessages.filter(msg => msg.includes('DEBUG') || msg.includes('Company section rendering'));
    if (debugMessages.length > 0) {
      console.log(`🔍 Debug console messages found:`);
      debugMessages.forEach((msg, index) => {
        console.log(`  ${index + 1}. ${msg}`);
      });
    } else {
      console.log(`❌ No debug console messages found - component may not be executing`);
    }

    // Get page source to see what's actually being rendered
    const pageSource = await page.content();
    console.log(`📄 Page source length: ${pageSource.length} characters`);

    // Check if this is a React app or static HTML
    const hasReactRoot = pageSource.includes('id="root"');
    const hasReactScripts = pageSource.includes('react') || pageSource.includes('React');
    const hasViteScripts = pageSource.includes('vite') || pageSource.includes('type="module"');

    console.log(`🔍 Page Analysis:`);
    console.log(`  - Has React root div: ${hasReactRoot}`);
    console.log(`  - Has React-related content: ${hasReactScripts}`);
    console.log(`  - Has Vite/module scripts: ${hasViteScripts}`);

    // Check if we're getting a 404 or error page
    const pageTitle = await page.title();
    console.log(`📄 Page title: "${pageTitle}"`);

    // Check for any error messages in the page
    const errorElements = await page.locator('text=/error|Error|404|not found/i').count();
    console.log(`❌ Error elements found: ${errorElements}`);

    // Check if main React bundle is loading
    const scriptTags = await page.evaluate(() => {
      const scripts = Array.from(document.querySelectorAll('script'));
      return scripts.map(script => ({
        src: script.src,
        type: script.type,
        hasContent: script.innerHTML.length > 0,
        contentPreview: script.innerHTML.substring(0, 100)
      }));
    });

    console.log(`📦 Script tags found: ${scriptTags.length}`);
    const mainScript = scriptTags.find(script => script.src.includes('main') || script.src.includes('index'));
    if (mainScript) {
      console.log(`📦 Main script found: ${mainScript.src}`);
    } else {
      console.log(`❌ No main script found`);
    }

    // Check for any network failures
    const failedRequests = [];
    page.on('requestfailed', request => {
      failedRequests.push({
        url: request.url(),
        failure: request.failure()
      });
    });

    // Wait a moment to see if any requests fail
    await page.waitForTimeout(2000);

    if (failedRequests.length > 0) {
      console.log(`🚨 Failed network requests:`);
      failedRequests.forEach((req, index) => {
        console.log(`  ${index + 1}. ${req.url} - ${req.failure?.errorText}`);
      });
    } else {
      console.log(`✅ No failed network requests detected`);
    }

    // Try to manually check if React is working by looking for React DevTools
    const reactDevToolsCheck = await page.evaluate(() => {
      return {
        hasReactDevTools: typeof window.__REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined',
        hasReactFiber: document.querySelector('[data-reactroot]') !== null,
        reactVersion: window.React ? window.React.version : 'not found',
        rootElement: document.getElementById('root') ? 'found' : 'not found',
        rootChildren: document.getElementById('root')?.children.length || 0
      };
    });

    console.log(`🔍 React DevTools Check:`, reactDevToolsCheck);

    // Debug: Try to evaluate JavaScript to check React component state
    try {
      const reactInfo = await page.evaluate(() => {
        // Check if React DevTools are available
        const hasReact = typeof window.React !== 'undefined';
        const hasReactDOM = typeof window.ReactDOM !== 'undefined';

        // Try to find the ProjectBasics component in the DOM
        const projectBasicsElement = document.querySelector('[class*="wizard-step-content"]');
        const hasProjectBasics = !!projectBasicsElement;

        // Check for company information in the DOM
        const companyElements = document.querySelectorAll('*');
        let companyTextCount = 0;
        companyElements.forEach(el => {
          if (el.textContent && el.textContent.includes('Company Information')) {
            companyTextCount++;
          }
        });

        return {
          hasReact,
          hasReactDOM,
          hasProjectBasics,
          companyTextCount,
          totalElements: document.querySelectorAll('*').length
        };
      });

      console.log(`🔍 React Debug Info:`, reactInfo);
    } catch (error) {
      console.log(`❌ Failed to evaluate React info: ${error.message}`);
    }

    // Debug: Get the actual HTML content around Company Information
    try {
      const companyHTML = await page.evaluate(() => {
        const elements = Array.from(document.querySelectorAll('*'));
        const companyElements = elements.filter(el =>
          el.textContent && el.textContent.includes('Company Information')
        );

        if (companyElements.length > 0) {
          // Get the first few company elements and their HTML
          return companyElements.slice(0, 3).map(el => ({
            tagName: el.tagName,
            className: el.className,
            innerHTML: el.innerHTML.substring(0, 200),
            outerHTML: el.outerHTML.substring(0, 300)
          }));
        }
        return [];
      });

      console.log(`📄 Company HTML Elements:`, JSON.stringify(companyHTML, null, 2));
    } catch (error) {
      console.log(`❌ Failed to get company HTML: ${error.message}`);
    }

    // Take a screenshot for visual debugging
    await page.screenshot({
      path: 'debug-project-creation-page.png',
      fullPage: true
    });
    console.log(`📸 Screenshot saved as debug-project-creation-page.png`);

    // Test all company fields with comprehensive selectors
    const fieldTests = [
      {
        name: 'Company Name',
        selectors: [
          'input[placeholder*="company name" i]',
          'input[placeholder="Enter company name"]',
          'label:has-text("Company Name") + * input',
          'div:has-text("Company Name") input',
          '[data-slot="input"][placeholder*="company" i]'
        ]
      },
      {
        name: 'Company Address',
        selectors: [
          'input[placeholder*="company address" i]',
          'input[placeholder="Enter company address"]',
          'label:has-text("Company Address") + * input',
          'div:has-text("Company Address") input',
          '[data-slot="input"][placeholder*="address" i]'
        ]
      },
      {
        name: 'City',
        selectors: [
          'input[placeholder*="city" i]',
          'input[placeholder="Enter city"]',
          'label:has-text("City") + * input',
          'div:has-text("City") input',
          '[data-slot="input"][placeholder*="city" i]'
        ]
      },
      {
        name: 'State',
        selectors: [
          'input[placeholder*="state" i]',
          'input[placeholder="Enter state/province"]',
          'label:has-text("State") + * input',
          'div:has-text("State") input',
          '[data-slot="input"][placeholder*="state" i]'
        ]
      },
      {
        name: 'Contact Email',
        selectors: [
          'input[placeholder*="contact email" i]',
          'input[placeholder="Enter contact email"]',
          'label:has-text("Contact Email") + * input',
          'div:has-text("Contact Email") input',
          '[data-slot="input"][placeholder*="email" i]'
        ]
      },
      {
        name: 'Signer Name',
        selectors: [
          'input[placeholder*="signer" i]',
          'input[placeholder*="authorized" i]',
          'input[placeholder="Enter name of person authorized to sign agreements"]',
          'label:has-text("Authorized Signer") + * input',
          'div:has-text("Authorized Signer") input',
          '[data-slot="input"][placeholder*="authorized" i]'
        ]
      },
      {
        name: 'Signer Title',
        selectors: [
          'input[placeholder*="title" i]',
          'input[placeholder*="CEO" i]',
          'label:has-text("Signer Title") + * input',
          'div:has-text("Signer Title") input',
          '[data-slot="input"][placeholder*="CEO" i]'
        ]
      }
    ];
    
    const visibleFields = [];
    
    for (const field of fieldTests) {
      let fieldFound = false;
      for (const selector of field.selectors) {
        const element = page.locator(selector).first();
        if (await element.isVisible({ timeout: 1000 })) {
          console.log(`✅ ${field.name} field is VISIBLE`);
          visibleFields.push(field.name);
          fieldFound = true;
          break;
        }
      }
      if (!fieldFound) {
        console.log(`❌ ${field.name} field is NOT visible`);
      }
    }
    
    console.log(`📊 Summary: ${visibleFields.length}/${fieldTests.length} company fields are visible`);
    console.log(`✅ Visible fields: ${visibleFields.join(', ')}`);
    
    await page.screenshot({ path: 'test-results/email-05-final-state.png', fullPage: true });
    
    // Step 6: Final Analysis
    console.log('📍 Step 6: Test Analysis');
    console.log(`🌐 Final URL: ${page.url()}`);
    console.log(`📊 Company fields found: ${visibleFields.length}/${fieldTests.length}`);
    console.log(`📄 Project page accessed: ${projectPageFound}`);
    
    if (visibleFields.length >= 5) {
      console.log('🎉 SUCCESS: Company information system is working!');
    } else if (projectPageFound) {
      console.log('⚠️ PARTIAL SUCCESS: Found project page but missing company fields');
    } else {
      console.log('❌ FAILURE: Could not access project creation system');
    }
  });
});
