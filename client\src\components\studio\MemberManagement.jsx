import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>dal<PERSON>ody, <PERSON>dal<PERSON>oot<PERSON>, Button, Card, CardBody, Avatar, Badge, Input } from '@heroui/react';
import { UserPlus, Mail, Trash2, Crown } from 'lucide-react';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';

const MemberManagement = ({ studioId, onClose, onUpdate }) => {
  const [members, setMembers] = useState([]);
  const [invitations, setInvitations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [inviteEmail, setInviteEmail] = useState('');
  const [inviteRole, setInviteRole] = useState('member');

  useEffect(() => {
    loadMembersAndInvitations();
  }, [studioId]);

  const loadMembersAndInvitations = async () => {
    try {
      setLoading(true);

      // Load current members
      const { data: membersData, error: membersError } = await supabase
        .from('team_members')
        .select(`
          *,
          users (
            id,
            display_name,
            avatar_url,
            email
          )
        `)
        .eq('team_id', studioId)
        .eq('status', 'active');

      if (membersError) throw membersError;

      // Load pending invitations
      const { data: invitesData, error: invitesError } = await supabase
        .from('team_invitations')
        .select('*')
        .eq('team_id', studioId)
        .eq('status', 'pending');

      if (invitesError) throw invitesError;

      setMembers(membersData || []);
      setInvitations(invitesData || []);

    } catch (error) {
      console.error('Error loading members:', error);
      toast.error('Failed to load member data');
    } finally {
      setLoading(false);
    }
  };

  const sendInvitation = async () => {
    try {
      if (!inviteEmail.trim()) {
        toast.error('Please enter an email address');
        return;
      }

      const { error } = await supabase
        .from('team_invitations')
        .insert({
          team_id: studioId,
          email: inviteEmail.trim(),
          role: inviteRole,
          status: 'pending'
        });

      if (error) throw error;

      toast.success('Invitation sent!');
      setInviteEmail('');
      loadMembersAndInvitations();

    } catch (error) {
      console.error('Error sending invitation:', error);
      toast.error('Failed to send invitation');
    }
  };

  const removeMember = async (memberId) => {
    try {
      const { error } = await supabase
        .from('team_members')
        .update({ status: 'removed' })
        .eq('id', memberId);

      if (error) throw error;

      toast.success('Member removed');
      loadMembersAndInvitations();
      if (onUpdate) onUpdate();

    } catch (error) {
      console.error('Error removing member:', error);
      toast.error('Failed to remove member');
    }
  };

  const updateMemberRole = async (memberId, newRole) => {
    try {
      const { error } = await supabase
        .from('team_members')
        .update({ role: newRole })
        .eq('id', memberId);

      if (error) throw error;

      toast.success('Member role updated');
      loadMembersAndInvitations();
      if (onUpdate) onUpdate();

    } catch (error) {
      console.error('Error updating role:', error);
      toast.error('Failed to update role');
    }
  };

  const getRoleColor = (role) => {
    switch (role) {
      case 'owner': return 'warning';
      case 'admin': return 'primary';
      case 'member': return 'default';
      default: return 'default';
    }
  };

  return (
    <Modal isOpen={true} onClose={onClose} size="4xl">
      <ModalContent>
        <ModalHeader>
          <h3>Manage Studio Members</h3>
        </ModalHeader>
        <ModalBody>
          {loading ? (
            <div className="text-center py-8">
              <p>Loading members...</p>
            </div>
          ) : (
            <div className="space-y-6">
              {/* Invite New Member */}
              <Card>
                <CardBody>
                  <h4 className="font-medium mb-4">Invite New Member</h4>
                  <div className="flex items-center space-x-2">
                    <Input
                      placeholder="Email address"
                      type="email"
                      value={inviteEmail}
                      onChange={(e) => setInviteEmail(e.target.value)}
                      className="flex-1"
                    />
                    <select
                      value={inviteRole}
                      onChange={(e) => setInviteRole(e.target.value)}
                      className="p-2 border rounded"
                    >
                      <option value="member">Member</option>
                      <option value="admin">Admin</option>
                    </select>
                    <Button
                      color="primary"
                      startContent={<Mail size={16} />}
                      onClick={sendInvitation}
                    >
                      Invite
                    </Button>
                  </div>
                </CardBody>
              </Card>

              {/* Current Members */}
              <div>
                <h4 className="font-medium mb-4">Current Members ({members.length})</h4>
                <div className="space-y-2">
                  {members.map((member) => (
                    <Card key={member.id}>
                      <CardBody className="flex flex-row items-center justify-between p-4">
                        <div className="flex items-center space-x-3">
                          <Avatar
                            src={member.users?.avatar_url}
                            name={member.users?.display_name}
                            size="sm"
                          />
                          <div>
                            <p className="font-medium">{member.users?.display_name}</p>
                            <p className="text-sm text-gray-500">{member.users?.email}</p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Badge color={getRoleColor(member.role)} variant="flat">
                            {member.role}
                          </Badge>
                          {member.role !== 'owner' && (
                            <>
                              <select
                                value={member.role}
                                onChange={(e) => updateMemberRole(member.id, e.target.value)}
                                className="text-sm p-1 border rounded"
                              >
                                <option value="member">Member</option>
                                <option value="admin">Admin</option>
                              </select>
                              <Button
                                size="sm"
                                color="danger"
                                variant="flat"
                                startContent={<Trash2 size={14} />}
                                onClick={() => removeMember(member.id)}
                              >
                                Remove
                              </Button>
                            </>
                          )}
                        </div>
                      </CardBody>
                    </Card>
                  ))}
                </div>
              </div>

              {/* Pending Invitations */}
              {invitations.length > 0 && (
                <div>
                  <h4 className="font-medium mb-4">Pending Invitations ({invitations.length})</h4>
                  <div className="space-y-2">
                    {invitations.map((invite) => (
                      <Card key={invite.id}>
                        <CardBody className="flex flex-row items-center justify-between p-4">
                          <div>
                            <p className="font-medium">{invite.email}</p>
                            <p className="text-sm text-gray-500">Invited as {invite.role}</p>
                          </div>
                          <Badge color="warning" variant="flat">
                            Pending
                          </Badge>
                        </CardBody>
                      </Card>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </ModalBody>
        <ModalFooter>
          <Button variant="flat" onClick={onClose}>
            Close
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default MemberManagement;
