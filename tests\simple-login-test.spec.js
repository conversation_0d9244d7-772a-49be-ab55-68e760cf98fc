import { test, expect } from '@playwright/test';

test('Simple Login Test', async ({ page }) => {
    console.log('🔍 Testing actual login functionality...');

    // Go to the site
    await page.goto('https://royalty.technology');
    await page.waitForLoadState('networkidle');

    console.log('📄 Initial page loaded');
    console.log(`   🌐 URL: ${page.url()}`);

    // Check what's actually on the page
    const allButtons = await page.locator('button').count();
    const allInputs = await page.locator('input').count();
    console.log(`   🔘 Total buttons: ${allButtons}`);
    console.log(`   📝 Total inputs: ${allInputs}`);

    // Try to find and click any visible login-related elements
    const visibleSignInButtons = await page.locator('button:visible:has-text("Sign In")').count();
    const visibleLoginButtons = await page.locator('button:visible:has-text("Login")').count();
    console.log(`   👁️ Visible Sign In buttons: ${visibleSignInButtons}`);
    console.log(`   👁️ Visible Login buttons: ${visibleLoginButtons}`);

    // Fill login form if inputs exist
    const emailInputs = await page.locator('input[type="email"]').count();
    const passwordInputs = await page.locator('input[type="password"]').count();

    if (emailInputs > 0 && passwordInputs > 0) {
        await page.fill('input[type="email"]', '<EMAIL>');
        await page.fill('input[type="password"]', 'TestPassword123!');
        console.log('📝 Credentials filled');
    } else {
        console.log('❌ No email/password inputs found');
    }
    
    // Try different approaches to login
    if (visibleSignInButtons > 0) {
        console.log('🔘 Clicking visible Sign In button...');
        await page.click('button:visible:has-text("Sign In")');
        console.log('✅ Visible Sign In button clicked');
    } else if (visibleLoginButtons > 0) {
        console.log('🔘 Clicking visible Login button...');
        await page.click('button:visible:has-text("Login")');
        console.log('✅ Visible Login button clicked');
    } else {
        console.log('⚠️ No visible login buttons, trying keyboard submit...');
        await page.press('input[type="password"]', 'Enter');
        console.log('⌨️ Pressed Enter on password field');
    }
    
    // Wait for navigation or response
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    const finalUrl = page.url();
    const finalTitle = await page.title();
    
    console.log('📊 FINAL RESULTS:');
    console.log(`   🌐 Final URL: ${finalUrl}`);
    console.log(`   📄 Final Title: ${finalTitle}`);
    
    // Check if we're logged in by looking for dashboard/profile elements
    const loggedInElements = await page.locator('text=Dashboard, text=Profile, text=Track, text=Earn').count();
    console.log(`   🎯 Logged-in elements found: ${loggedInElements}`);
    
    // Check page content
    const bodyText = await page.locator('body').textContent();
    console.log(`   📝 Page content length: ${bodyText.length} characters`);
    
    if (bodyText.includes('Dashboard') || bodyText.includes('Track') || bodyText.includes('Earn')) {
        console.log('✅ LOGIN SUCCESSFUL - Dashboard elements detected');
    } else if (finalUrl.includes('/login')) {
        console.log('❌ LOGIN FAILED - Still on login page');
        console.log(`   📝 Page content preview: ${bodyText.substring(0, 300)}...`);
    } else {
        console.log('⚠️ UNCLEAR STATUS - Need manual verification');
        console.log(`   📝 Page content preview: ${bodyText.substring(0, 300)}...`);
    }
    
    // Keep browser open for 30 seconds for manual inspection
    console.log('⏸️ Keeping browser open for manual inspection...');
    await page.waitForTimeout(30000);
});
