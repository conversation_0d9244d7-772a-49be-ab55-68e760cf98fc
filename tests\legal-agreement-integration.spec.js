import { test, expect } from '@playwright/test';

const PRODUCTION_URL = 'https://royalty.technology';
const TEST_EMAIL = '<EMAIL>';
const TEST_PASSWORD = 'TestPassword123!';

test.describe('Legal Agreement Integration Test', () => {
    test.beforeEach(async ({ page }) => {
        console.log('🔐 Attempting authentication...');
        await page.goto(PRODUCTION_URL);
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(2000);

        // Fill credentials
        await page.fill('input[type="email"]', TEST_EMAIL);
        await page.fill('input[type="password"]', TEST_PASSWORD);
        
        // Submit login
        await page.click('button[type="submit"]');
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(3000);
        
        console.log('✅ Authentication successful');
    });

    test('Legal Agreement System Integration Check', async ({ page }) => {
        console.log('📋 Testing Legal Agreement System Integration...');
        
        const results = {
            projectWizardAccess: false,
            agreementStepExists: false,
            agreementGeneration: false,
            agreementPreview: false,
            existingProjectAgreements: false,
            contributorAgreements: false,
            agreementTemplates: false
        };

        // 1. Check Project Creation Wizard Access
        console.log('🚀 Step 1: Checking Project Creation Wizard...');
        await page.goto(`${PRODUCTION_URL}/track`);
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(2000);
        
        // Look for project creation buttons
        const createButtons = await page.locator('button').filter({ hasText: /create|new|add/i }).count();
        const createLinks = await page.locator('a').filter({ hasText: /create|new|add/i }).count();
        
        results.projectWizardAccess = createButtons > 0 || createLinks > 0;
        console.log(`📊 Project Wizard Access: ${results.projectWizardAccess ? '✅' : '❌'} (${createButtons} buttons, ${createLinks} links)`);

        // 2. Check if Project Creation Wizard Has Agreement Step
        console.log('📝 Step 2: Checking Project Wizard Agreement Step...');
        
        // Try to access project creation directly
        await page.goto(`${PRODUCTION_URL}/projects/create`);
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(3000);
        
        const wizardContent = await page.textContent('body');
        const hasAgreementStep = wizardContent.toLowerCase().includes('agreement') ||
                               wizardContent.toLowerCase().includes('legal') ||
                               wizardContent.toLowerCase().includes('contract') ||
                               wizardContent.toLowerCase().includes('review agreement');
        
        // Check for step indicators that might show agreement step
        const stepElements = await page.locator('[class*="step"], [data-testid*="step"], .wizard-step').count();
        
        results.agreementStepExists = hasAgreementStep || stepElements >= 7; // Should have 7 steps including agreement
        console.log(`📊 Agreement Step Exists: ${results.agreementStepExists ? '✅' : '❌'} (${stepElements} steps found)`);

        // 3. Check Agreement Generation Functionality
        console.log('⚙️ Step 3: Checking Agreement Generation...');
        
        // Look for agreement generation buttons or functionality
        const agreementButtons = await page.locator('button').filter({ hasText: /generate|agreement|legal/i }).count();
        
        // Check if agreement template is accessible
        try {
            const templateResponse = await page.request.get(`${PRODUCTION_URL}/contributor-agreement-template.md`);
            const templateExists = templateResponse.ok();
            
            results.agreementGeneration = agreementButtons > 0 || templateExists;
            console.log(`📊 Agreement Generation: ${results.agreementGeneration ? '✅' : '❌'} (${agreementButtons} buttons, template: ${templateExists})`);
        } catch (error) {
            console.log('⚠️ Could not check agreement template');
            results.agreementGeneration = agreementButtons > 0;
            console.log(`📊 Agreement Generation: ${results.agreementGeneration ? '✅' : '❌'} (${agreementButtons} buttons)`);
        }

        // 4. Check Agreement Preview Functionality
        console.log('👁️ Step 4: Checking Agreement Preview...');
        
        // Look for preview functionality
        const previewElements = await page.locator('[class*="preview"], [data-testid*="preview"]').count();
        const previewButtons = await page.locator('button').filter({ hasText: /preview|view|show/i }).count();
        
        results.agreementPreview = previewElements > 0 || previewButtons > 0;
        console.log(`📊 Agreement Preview: ${results.agreementPreview ? '✅' : '❌'} (${previewElements} elements, ${previewButtons} buttons)`);

        // 5. Check Existing Project Agreements
        console.log('📁 Step 5: Checking Existing Project Agreements...');
        
        // Go back to track page to check existing projects
        await page.goto(`${PRODUCTION_URL}/track`);
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(2000);
        
        // Look for project cards or elements
        const projectElements = await page.locator('[data-testid*="project"], .project, [class*="project"]').count();
        
        if (projectElements > 0) {
            console.log(`🔍 Found ${projectElements} project elements, checking for agreement access...`);
            
            // Check if projects have agreement-related functionality
            const trackContent = await page.textContent('body');
            const hasProjectAgreements = trackContent.toLowerCase().includes('agreement') ||
                                       trackContent.toLowerCase().includes('legal') ||
                                       trackContent.toLowerCase().includes('contract');
            
            // Look for agreement buttons in project context
            const projectAgreementButtons = await page.locator('button').filter({ hasText: /agreement|legal|contract/i }).count();
            
            results.existingProjectAgreements = hasProjectAgreements || projectAgreementButtons > 0;
            console.log(`📊 Existing Project Agreements: ${results.existingProjectAgreements ? '✅' : '❌'} (${projectAgreementButtons} buttons)`);
        } else {
            console.log('⚠️ No existing projects found to check agreements');
            results.existingProjectAgreements = false;
        }

        // 6. Check Contributor Agreement Management
        console.log('👥 Step 6: Checking Contributor Agreement Management...');
        
        // Check if there's a way to manage contributor agreements
        const contributorButtons = await page.locator('button').filter({ hasText: /contributor|member|team/i }).count();
        
        // Check for contributor management pages
        try {
            await page.goto(`${PRODUCTION_URL}/contributors`);
            await page.waitForLoadState('networkidle');
            await page.waitForTimeout(2000);
            
            const contributorPageContent = await page.textContent('body');
            const hasContributorAgreements = contributorPageContent.toLowerCase().includes('agreement') ||
                                           contributorPageContent.toLowerCase().includes('legal') ||
                                           contributorPageContent.toLowerCase().includes('contract');
            
            results.contributorAgreements = hasContributorAgreements;
            console.log(`📊 Contributor Agreements: ${results.contributorAgreements ? '✅' : '❌'}`);
        } catch (error) {
            console.log('⚠️ No dedicated contributor page found');
            results.contributorAgreements = contributorButtons > 0;
            console.log(`📊 Contributor Agreements: ${results.contributorAgreements ? '✅' : '❌'} (${contributorButtons} buttons)`);
        }

        // 7. Check Agreement Templates Accessibility
        console.log('📄 Step 7: Checking Agreement Templates...');
        
        // Check if agreement templates are accessible
        const templatePaths = [
            '/contributor-agreement-template.md',
            '/simplified-contributor-agreement.md',
            '/detailed-contributor-agreement.md'
        ];
        
        let accessibleTemplates = 0;
        for (const templatePath of templatePaths) {
            try {
                const response = await page.request.get(`${PRODUCTION_URL}${templatePath}`);
                if (response.ok()) {
                    accessibleTemplates++;
                    console.log(`✅ Template accessible: ${templatePath}`);
                }
            } catch (error) {
                console.log(`❌ Template not accessible: ${templatePath}`);
            }
        }
        
        results.agreementTemplates = accessibleTemplates > 0;
        console.log(`📊 Agreement Templates: ${results.agreementTemplates ? '✅' : '❌'} (${accessibleTemplates}/${templatePaths.length} accessible)`);

        // Summary
        console.log('\n📋 LEGAL AGREEMENT SYSTEM RESULTS:');
        console.log('=====================================');
        console.log(`🚀 Project Wizard Access: ${results.projectWizardAccess ? '✅ WORKING' : '❌ FAILED'}`);
        console.log(`📝 Agreement Step Exists: ${results.agreementStepExists ? '✅ WORKING' : '❌ FAILED'}`);
        console.log(`⚙️ Agreement Generation: ${results.agreementGeneration ? '✅ WORKING' : '❌ FAILED'}`);
        console.log(`👁️ Agreement Preview: ${results.agreementPreview ? '✅ WORKING' : '❌ FAILED'}`);
        console.log(`📁 Existing Project Agreements: ${results.existingProjectAgreements ? '✅ WORKING' : '❌ FAILED'}`);
        console.log(`👥 Contributor Agreements: ${results.contributorAgreements ? '✅ WORKING' : '❌ FAILED'}`);
        console.log(`📄 Agreement Templates: ${results.agreementTemplates ? '✅ WORKING' : '❌ FAILED'}`);
        
        const workingFeatures = Object.values(results).filter(Boolean).length;
        const totalFeatures = Object.keys(results).length;
        
        console.log(`\n📊 LEGAL AGREEMENT SYSTEM READINESS: ${workingFeatures}/${totalFeatures} features working (${Math.round(workingFeatures/totalFeatures*100)}%)`);
        
        // Store results for analysis
        await page.evaluate((results) => {
            window.legalAgreementResults = results;
        }, results);
        
        // For a functional legal agreement system, we need at least 4/7 features working
        expect(workingFeatures).toBeGreaterThanOrEqual(4);
    });
});
