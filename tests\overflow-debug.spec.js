import { test, expect } from '@playwright/test';

test.describe('Horizontal Overflow Debug', () => {
  test('Identify specific elements causing horizontal overflow', async ({ page }) => {
    console.log('🔍 Starting horizontal overflow debugging...');
    
    // Navigate to production site
    await page.goto('https://royalty.technology');
    await page.waitForLoadState('networkidle');
    
    // Login first
    console.log('🔐 Logging in...');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'TestPassword123!');
    await page.click('button:has-text("Log In")');
    await page.waitForURL('**/dashboard');
    await page.waitForLoadState('networkidle');
    
    // Test each major page
    const pages = [
      { name: 'Dashboard', url: '/dashboard' },
      { name: 'Start', url: '/start' },
      { name: 'Track', url: '/track' },
      { name: 'Earn', url: '/earn' }
    ];
    
    for (const pageInfo of pages) {
      console.log(`\n🔍 Analyzing ${pageInfo.name} page...`);
      
      await page.goto(`https://royalty.technology${pageInfo.url}`);
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(1000); // Let animations settle
      
      // Get detailed overflow information
      const overflowDetails = await page.evaluate(() => {
        const details = [];
        const elements = document.querySelectorAll('*');
        
        for (const el of elements) {
          if (el.scrollWidth > el.clientWidth + 5) {
            const rect = el.getBoundingClientRect();
            const computedStyle = window.getComputedStyle(el);
            
            details.push({
              tagName: el.tagName.toLowerCase(),
              className: el.className,
              id: el.id,
              scrollWidth: el.scrollWidth,
              clientWidth: el.clientWidth,
              overflow: el.scrollWidth - el.clientWidth,
              position: computedStyle.position,
              width: computedStyle.width,
              maxWidth: computedStyle.maxWidth,
              minWidth: computedStyle.minWidth,
              left: rect.left,
              right: rect.right,
              display: computedStyle.display,
              flexBasis: computedStyle.flexBasis,
              gridColumn: computedStyle.gridColumn,
              transform: computedStyle.transform,
              textContent: el.textContent ? el.textContent.substring(0, 50) + '...' : ''
            });
          }
        }
        
        // Sort by overflow amount (largest first)
        return details.sort((a, b) => b.overflow - a.overflow).slice(0, 10);
      });
      
      console.log(`📊 ${pageInfo.name} - Found ${overflowDetails.length} elements with horizontal overflow:`);
      
      overflowDetails.forEach((detail, index) => {
        console.log(`\n  ${index + 1}. ${detail.tagName}${detail.id ? '#' + detail.id : ''}${detail.className ? '.' + detail.className.split(' ')[0] : ''}`);
        console.log(`     Overflow: ${detail.overflow}px (${detail.scrollWidth}px scroll vs ${detail.clientWidth}px client)`);
        console.log(`     Position: ${detail.position}, Width: ${detail.width}, Display: ${detail.display}`);
        if (detail.transform && detail.transform !== 'none') {
          console.log(`     Transform: ${detail.transform}`);
        }
        if (detail.textContent.trim()) {
          console.log(`     Content: "${detail.textContent.trim()}"`);
        }
        console.log(`     Location: left=${detail.left}px, right=${detail.right}px`);
      });
      
      // Check viewport width
      const viewportWidth = await page.evaluate(() => window.innerWidth);
      console.log(`\n📐 Viewport width: ${viewportWidth}px`);
      
      // Check if any elements extend beyond viewport
      const elementsOutsideViewport = await page.evaluate(() => {
        const elements = document.querySelectorAll('*');
        const viewportWidth = window.innerWidth;
        const outsideElements = [];
        
        for (const el of elements) {
          const rect = el.getBoundingClientRect();
          if (rect.right > viewportWidth + 5) { // 5px tolerance
            outsideElements.push({
              tagName: el.tagName.toLowerCase(),
              className: el.className,
              id: el.id,
              right: rect.right,
              width: rect.width,
              overflow: rect.right - viewportWidth
            });
          }
        }
        
        return outsideElements.sort((a, b) => b.overflow - a.overflow).slice(0, 5);
      });
      
      if (elementsOutsideViewport.length > 0) {
        console.log(`\n🚨 Elements extending beyond viewport (${viewportWidth}px):`);
        elementsOutsideViewport.forEach((el, index) => {
          console.log(`  ${index + 1}. ${el.tagName}${el.id ? '#' + el.id : ''}${el.className ? '.' + el.className.split(' ')[0] : ''}`);
          console.log(`     Right edge: ${el.right}px (${el.overflow}px beyond viewport)`);
          console.log(`     Width: ${el.width}px`);
        });
      }
    }
    
    console.log('\n✅ Overflow debugging completed');
  });
});
