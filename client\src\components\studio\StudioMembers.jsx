import React, { useState } from 'react';
import { Card, Card<PERSON>ody, CardHeader, Button, Avatar, Badge, <PERSON>dal, <PERSON>dal<PERSON>ontent, <PERSON>dal<PERSON>eader, ModalBody, ModalFooter, Input } from '@heroui/react';
import { UserPlus, Mail } from 'lucide-react';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';

const StudioMembers = ({ studioId, members = [], onMembersUpdate }) => {
  const [showInviteModal, setShowInviteModal] = useState(false);
  const [inviteEmail, setInviteEmail] = useState('');
  const [inviteRole, setInviteRole] = useState('member');
  const [loading, setLoading] = useState(false);

  const handleInviteMember = () => {
    setShowInviteModal(true);
  };

  const sendInvite = async () => {
    try {
      setLoading(true);

      if (!inviteEmail.trim()) {
        toast.error('Please enter an email address');
        return;
      }

      // Create team invitation
      const { error } = await supabase
        .from('team_invitations')
        .insert({
          team_id: studioId,
          email: inviteEmail.trim(),
          role: inviteRole,
          status: 'pending'
        });

      if (error) throw error;

      toast.success('Invitation sent successfully!');
      setShowInviteModal(false);
      setInviteEmail('');
      setInviteRole('member');

      if (onMembersUpdate) {
        onMembersUpdate();
      }

    } catch (error) {
      console.error('Error sending invite:', error);
      toast.error('Failed to send invitation');
    } finally {
      setLoading(false);
    }
  };

  const getRoleColor = (role) => {
    switch (role) {
      case 'founder': return 'warning';
      case 'owner': return 'warning';
      case 'admin': return 'primary';
      case 'member': return 'default';
      default: return 'default';
    }
  };

  const getCollaborationTypeIcon = (type) => {
    switch (type) {
      case 'studio_member': return '👥';
      case 'contractor': return '🤝';
      case 'specialist': return '⭐';
      default: return '👤';
    }
  };

  return (
    <Card>
      <CardHeader className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Studio Members</h3>
        <Button
          size="sm"
          color="primary"
          startContent={<UserPlus size={16} />}
          onClick={handleInviteMember}
        >
          Invite
        </Button>
      </CardHeader>
      <CardBody>
        {members.length === 0 ? (
          <div className="text-center py-8">
            <div className="text-gray-400 mb-4">👥</div>
            <p className="text-gray-600 mb-4">No members yet</p>
            <Button
              color="primary"
              variant="flat"
              startContent={<Mail size={16} />}
              onClick={handleInviteMember}
            >
              Invite First Member
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            {members.map((member) => (
              <div key={member.id} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center space-x-3">
                  <Avatar
                    src={member.users?.avatar_url}
                    name={member.users?.display_name}
                    size="sm"
                  />
                  <div>
                    <p className="font-medium text-sm">{member.users?.display_name}</p>
                    <div className="flex items-center space-x-2 text-xs text-gray-500">
                      <span>{getCollaborationTypeIcon(member.collaboration_type)}</span>
                      <span>{member.collaboration_type?.replace('_', ' ')}</span>
                      {member.engagement_duration && (
                        <>
                          <span>•</span>
                          <span>{member.engagement_duration}</span>
                        </>
                      )}
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Badge 
                    color={getRoleColor(member.role)} 
                    variant="flat" 
                    size="sm"
                  >
                    {member.role}
                  </Badge>
                  {member.status === 'pending' && (
                    <Badge color="warning" variant="flat" size="sm">
                      Pending
                    </Badge>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </CardBody>

      {/* Invite Member Modal */}
      <Modal isOpen={showInviteModal} onClose={() => setShowInviteModal(false)}>
        <ModalContent>
          <ModalHeader>
            <h3>Invite Studio Member</h3>
          </ModalHeader>
          <ModalBody>
            <div className="space-y-4">
              <Input
                label="Email Address"
                type="email"
                placeholder="Enter member's email"
                value={inviteEmail}
                onChange={(e) => setInviteEmail(e.target.value)}
              />
              <select
                value={inviteRole}
                onChange={(e) => setInviteRole(e.target.value)}
                className="w-full p-3 border rounded-lg"
              >
                <option value="member">Member</option>
                <option value="admin">Admin</option>
                <option value="owner">Owner</option>
              </select>
            </div>
          </ModalBody>
          <ModalFooter>
            <Button
              variant="flat"
              onClick={() => setShowInviteModal(false)}
            >
              Cancel
            </Button>
            <Button
              color="primary"
              onClick={sendInvite}
              isLoading={loading}
              startContent={<Mail size={16} />}
            >
              Send Invite
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Card>
  );
};

export default StudioMembers;
