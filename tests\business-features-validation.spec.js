import { test, expect } from '@playwright/test';

const PRODUCTION_URL = 'https://royalty.technology';
const TEST_EMAIL = '<EMAIL>';
const TEST_PASSWORD = 'TestPassword123!';

test.describe('Business Features Validation', () => {
    test.beforeEach(async ({ page }) => {
        console.log('🔐 Attempting authentication...');
        await page.goto(PRODUCTION_URL);
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(2000);

        // Fill credentials
        console.log('📝 Filling in credentials...');
        await page.fill('input[type="email"]', TEST_EMAIL);
        await page.fill('input[type="password"]', TEST_PASSWORD);
        
        // Submit login
        await page.click('button[type="submit"]');
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(3000);
        
        console.log('✅ Authentication successful');
    });

    test('1. Add Contributors Feature', async ({ page }) => {
        console.log('👥 Testing Add Contributors Feature...');
        
        // Check for contributor management in projects
        await page.goto(`${PRODUCTION_URL}/track`);
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(3000);
        
        // Look for contributor-related elements
        const contributorButtons = await page.locator('text="Add Contributor"').count() +
            await page.locator('text="Invite"').count() +
            await page.locator('text="Member"').count() +
            await page.locator('text="Contributor"').count();
        const teamElements = await page.locator('[data-testid*="team"], [data-testid*="member"], [data-testid*="contributor"]').count();
        
        console.log(`📊 Contributor buttons found: ${contributorButtons}`);
        console.log(`📊 Team elements found: ${teamElements}`);
        
        // Check studios page for team management
        await page.goto(`${PRODUCTION_URL}/studios`);
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(2000);
        
        const studioTeamElements = await page.locator('text="Team"').count() +
            await page.locator('text="Member"').count() +
            await page.locator('text="Add"').count();
        console.log(`📊 Studio team elements: ${studioTeamElements}`);
        
        // Should have some contributor management functionality
        expect(contributorButtons + teamElements + studioTeamElements).toBeGreaterThan(0);
    });

    test('2. Add Friends Feature', async ({ page }) => {
        console.log('👫 Testing Add Friends Feature...');
        
        // Check profile page for friends functionality
        await page.goto(`${PRODUCTION_URL}/profile`);
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(3000);
        
        const friendsElements = await page.locator('text="Friend"').count() +
            await page.locator('text="Connect"').count() +
            await page.locator('text="Network"').count() +
            await page.locator('text="Follow"').count();
        const socialElements = await page.locator('[data-testid*="friend"], [data-testid*="social"], [data-testid*="network"]').count();
        
        console.log(`📊 Friends elements found: ${friendsElements}`);
        console.log(`📊 Social elements found: ${socialElements}`);
        
        // Check if there's a dedicated social/network section
        const pageContent = await page.textContent('body');
        const hasSocialFeatures = pageContent.includes('friend') || pageContent.includes('network') || pageContent.includes('connect');
        
        console.log(`🔍 Has social features: ${hasSocialFeatures}`);
        
        // Friends feature should be present
        expect(friendsElements + socialElements).toBeGreaterThan(0);
    });

    test('3. Customize Profile Feature', async ({ page }) => {
        console.log('⚙️ Testing Profile Customization...');
        
        await page.goto(`${PRODUCTION_URL}/profile`);
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(3000);
        
        // Look for profile editing elements
        const editButtons = await page.locator('text="Edit"').count() +
            await page.locator('text="Update"').count() +
            await page.locator('text="Save"').count() +
            await page.locator('text="Customize"').count();
        const profileFields = await page.locator('input, textarea, select').count();
        const settingsElements = await page.locator('[data-testid*="setting"], [data-testid*="edit"], [data-testid*="profile"]').count();
        
        console.log(`📊 Edit buttons found: ${editButtons}`);
        console.log(`📊 Profile fields found: ${profileFields}`);
        console.log(`📊 Settings elements found: ${settingsElements}`);
        
        // Check for profile customization options
        const pageContent = await page.textContent('body');
        const hasCustomization = pageContent.includes('edit') || pageContent.includes('update') || pageContent.includes('customize');
        
        console.log(`🔍 Has customization features: ${hasCustomization}`);
        
        // Profile customization should be available
        expect(editButtons + profileFields).toBeGreaterThan(0);
    });

    test('4. See Contributions Feature', async ({ page }) => {
        console.log('📈 Testing View Contributions Feature...');
        
        // Check dashboard for contributions
        await page.goto(`${PRODUCTION_URL}/dashboard`);
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(3000);
        
        const contributionElements = await page.locator('text="Contribution"').count() +
            await page.locator('text="Activity"').count() +
            await page.locator('text="History"').count() +
            await page.locator('text="Progress"').count();
        const statsElements = await page.locator('[data-testid*="stat"], [data-testid*="contribution"], [data-testid*="activity"]').count();
        
        console.log(`📊 Contribution elements found: ${contributionElements}`);
        console.log(`📊 Stats elements found: ${statsElements}`);
        
        // Check profile for contribution history
        await page.goto(`${PRODUCTION_URL}/profile`);
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(2000);
        
        const profileContributions = await page.locator('text="Contribution"').count() +
            await page.locator('text="Work"').count() +
            await page.locator('text="Project"').count() +
            await page.locator('text="Task"').count();
        console.log(`📊 Profile contributions: ${profileContributions}`);
        
        // Should show contribution data
        expect(contributionElements + statsElements + profileContributions).toBeGreaterThan(0);
    });

    test('5. Make Gigwork Requests Feature', async ({ page }) => {
        console.log('💼 Testing Gigwork Request Creation...');
        
        // Check earn page for posting work
        await page.goto(`${PRODUCTION_URL}/earn`);
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(3000);
        
        const postWorkElements = await page.locator('text="Post"').count() +
            await page.locator('text="Create"').count() +
            await page.locator('text="Request"').count() +
            await page.locator('text="Job"').count() +
            await page.locator('text="Gig"').count();
        const createButtons = await page.locator('button').filter({ hasText: /post|create|add|new/i }).count();
        
        console.log(`📊 Post work elements found: ${postWorkElements}`);
        console.log(`📊 Create buttons found: ${createButtons}`);
        
        // Check track page for project/task creation
        await page.goto(`${PRODUCTION_URL}/track`);
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(2000);
        
        const taskCreationElements = await page.locator('text="New Task"').count() +
            await page.locator('text="Add Task"').count() +
            await page.locator('text="Create"').count() +
            await page.locator('button').filter({ hasText: /new|add|create/i }).count();
        console.log(`📊 Task creation elements: ${taskCreationElements}`);
        
        // Should have work posting capability
        expect(postWorkElements + createButtons + taskCreationElements).toBeGreaterThan(0);
    });

    test('6. Apply for Gigwork Feature', async ({ page }) => {
        console.log('🎯 Testing Gigwork Application Feature...');
        
        await page.goto(`${PRODUCTION_URL}/earn`);
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(3000);
        
        // Look for available work and apply buttons
        const applyElements = await page.locator('text="Apply"').count() +
            await page.locator('text="Join"').count() +
            await page.locator('text="Accept"').count() +
            await page.locator('text="Take"').count();
        const workListings = await page.locator('[data-testid*="job"], [data-testid*="gig"], [data-testid*="mission"], [data-testid*="bounty"]').count();
        const actionButtons = await page.locator('button').filter({ hasText: /apply|join|accept|take/i }).count();
        
        console.log(`📊 Apply elements found: ${applyElements}`);
        console.log(`📊 Work listings found: ${workListings}`);
        console.log(`📊 Action buttons found: ${actionButtons}`);
        
        // Check for mission/bounty board
        const pageContent = await page.textContent('body');
        const hasWorkListings = pageContent.includes('mission') || pageContent.includes('bounty') || pageContent.includes('gig');
        
        console.log(`🔍 Has work listings: ${hasWorkListings}`);
        
        // Should have work application functionality
        expect(applyElements + workListings + actionButtons).toBeGreaterThan(0);
    });

    test('7. Legal Agreements Feature', async ({ page }) => {
        console.log('📋 Testing Legal Agreements Feature...');
        
        // Check for legal/agreements section
        await page.goto(`${PRODUCTION_URL}/legal`);
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(3000);
        
        let legalPageExists = true;
        try {
            await page.waitForSelector('main', { timeout: 5000 });
        } catch {
            legalPageExists = false;
            console.log('⚠️ No dedicated legal page found');
        }
        
        // Check studios for agreement generation
        await page.goto(`${PRODUCTION_URL}/studios/create`);
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(3000);
        
        const agreementElements = await page.locator('text="Agreement"').count() +
            await page.locator('text="Contract"').count() +
            await page.locator('text="Legal"').count() +
            await page.locator('text="Terms"').count();
        const legalButtons = await page.locator('button').filter({ hasText: /agreement|contract|legal|terms/i }).count();
        
        console.log(`📊 Agreement elements found: ${agreementElements}`);
        console.log(`📊 Legal buttons found: ${legalButtons}`);
        
        // Check profile/settings for agreements
        await page.goto(`${PRODUCTION_URL}/profile`);
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(2000);
        
        const profileLegalElements = await page.locator('text="Agreement"').count() +
            await page.locator('text="Contract"').count() +
            await page.locator('text="Document"').count();
        console.log(`📊 Profile legal elements: ${profileLegalElements}`);
        
        // Should have legal agreement functionality
        expect(agreementElements + legalButtons + profileLegalElements).toBeGreaterThan(0);
    });

    test('8. Overall Business Feature Integration', async ({ page }) => {
        console.log('🔗 Testing Overall Business Feature Integration...');
        
        const featureResults = {
            dashboard: false,
            profile: false,
            track: false,
            earn: false,
            studios: false
        };
        
        // Test each major page for business functionality
        const pages = [
            { url: '/dashboard', key: 'dashboard' },
            { url: '/profile', key: 'profile' },
            { url: '/track', key: 'track' },
            { url: '/earn', key: 'earn' },
            { url: '/studios', key: 'studios' }
        ];
        
        for (const pageInfo of pages) {
            try {
                await page.goto(`${PRODUCTION_URL}${pageInfo.url}`);
                await page.waitForLoadState('networkidle');
                await page.waitForTimeout(2000);
                
                // Check for business-related content
                const businessElements = await page.locator('button, input, [data-testid]').count() +
                    await page.locator('text="Add"').count() +
                    await page.locator('text="Create"').count() +
                    await page.locator('text="Edit"').count() +
                    await page.locator('text="Apply"').count();
                featureResults[pageInfo.key] = businessElements > 0;
                
                console.log(`📊 ${pageInfo.key}: ${businessElements} business elements`);
            } catch (e) {
                console.log(`❌ ${pageInfo.key} error:`, e.message);
            }
        }
        
        console.log('🎯 Feature Results:', featureResults);
        
        const workingFeatures = Object.values(featureResults).filter(Boolean).length;
        const totalFeatures = Object.keys(featureResults).length;
        
        console.log(`✅ Working features: ${workingFeatures}/${totalFeatures}`);
        
        // Should have most business features working
        expect(workingFeatures).toBeGreaterThanOrEqual(4);
    });
});
