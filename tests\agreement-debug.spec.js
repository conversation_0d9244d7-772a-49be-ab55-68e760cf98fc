import { test, expect } from '@playwright/test';

test.describe('Agreement Generation Debug', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to login page and authenticate
    await page.goto('https://royalty.technology/login');
    await page.waitForLoadState('networkidle');
    
    // Login with test credentials
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'TestPassword123!');
    await page.click('button[type="submit"]');
    
    // Wait for authentication to complete
    await page.waitForURL('**/dashboard', { timeout: 10000 });
    await page.waitForLoadState('networkidle');
  });

  test('Debug Agreement Generation Console Logs', async ({ page }) => {
    console.log('🔍 Starting Agreement Generation Debug...');
    
    // Listen to console logs
    const consoleLogs = [];
    page.on('console', msg => {
      const text = msg.text();
      consoleLogs.push(text);
      console.log(`[BROWSER] ${text}`);
    });

    // Listen to page errors
    page.on('pageerror', error => {
      console.log(`[PAGE ERROR] ${error.message}`);
    });

    // Navigate to project creation
    await page.goto('https://royalty.technology/project/create');
    await page.waitForLoadState('networkidle');
    
    // Fill basic project information
    await page.fill('input[placeholder="Enter project name"]', 'Debug Agreement Test');
    await page.fill('textarea[placeholder="Describe your project"]', 'Test project for debugging agreement generation');
    
    // Navigate through wizard steps to reach Review & Agreement
    for (let i = 0; i < 6; i++) {
      await page.waitForTimeout(1000);
      
      const nextBtn = page.locator('button:has-text("Next"), button:has-text("Continue")').first();
      if (await nextBtn.isVisible()) {
        await nextBtn.click({ force: true });
        await page.waitForTimeout(1500);
      }
    }
    
    // Wait for Review & Agreement page to load
    await page.waitForTimeout(5000);
    
    // Check if we're on the Review & Agreement step
    const reviewTitle = page.locator('h2:has-text("Review & Agreement"), h3:has-text("Review & Agreement"), .step-title:has-text("Review")');
    const isOnReviewStep = await reviewTitle.count() > 0;
    
    if (isOnReviewStep) {
      console.log('✅ Successfully reached Review & Agreement step');
      
      // Wait for agreement generation to complete
      await page.waitForTimeout(10000);
      
      // Check for regenerate button and click it to trigger generation
      const regenerateBtn = page.locator('button:has-text("Regenerate"), .btn-regenerate');
      if (await regenerateBtn.isVisible()) {
        console.log('🔄 Clicking regenerate button to trigger agreement generation...');
        await regenerateBtn.click();
        
        // Wait for regeneration to complete
        await page.waitForTimeout(10000);
      }
      
      // Check if agreement content is present
      const agreementContent = page.locator('.agreement-content, .agreement-preview');
      const hasContent = await agreementContent.count() > 0;
      
      if (hasContent) {
        const contentText = await agreementContent.first().textContent();
        console.log(`📄 Agreement content length: ${contentText?.length || 0} characters`);
        
        if (contentText && contentText.length > 100) {
          console.log('✅ Agreement has substantial content');
          console.log(`📝 First 200 characters: ${contentText.substring(0, 200)}...`);
        } else {
          console.log('⚠️ Agreement content is minimal or empty');
          console.log(`📝 Full content: "${contentText}"`);
        }
      } else {
        console.log('❌ No agreement content found');
      }
      
    } else {
      console.log('❌ Failed to reach Review & Agreement step');
      
      // Check what step we're on
      const currentStep = await page.locator('.step-title, h2, h3').first().textContent();
      console.log(`📍 Current step: ${currentStep}`);
    }
    
    // Take screenshot for debugging
    await page.screenshot({ 
      path: 'test-results/agreement-debug.png',
      fullPage: true 
    });
    
    // Print all console logs for analysis
    console.log('\n📋 All Console Logs:');
    consoleLogs.forEach((log, index) => {
      console.log(`${index + 1}. ${log}`);
    });
    
    console.log('🏁 Agreement generation debug completed');
  });
});
