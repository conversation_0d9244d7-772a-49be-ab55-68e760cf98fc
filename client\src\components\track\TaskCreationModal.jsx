import React, { useState, useContext } from 'react';
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON>onte<PERSON>, 
  <PERSON>dal<PERSON>eader, 
  <PERSON>dalBody, 
  <PERSON>dal<PERSON>ooter,
  Button,
  Input,
  Textarea,
  Select,
  SelectItem,
  DatePicker
} from '@heroui/react';
import { motion } from 'framer-motion';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';
import {
  Plus,
  Calendar,
  User,
  FileText
} from 'lucide-react';

/**
 * Task Creation Modal Component
 * 
 * Elegant modal for creating tasks without leaving the page.
 * Features form validation and integration with the task system.
 */
const TaskCreationModal = ({
  isOpen,
  onClose,
  onTaskCreated,
  projectId = null,
  projectName = null,
  className = ""
}) => {
  const { currentUser } = useContext(UserContext);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    status: 'todo',
    assignee: '',
    due_date: null,
    estimated_hours: ''
  });



  const statusOptions = [
    { key: 'todo', label: 'To Do' },
    { key: 'in_progress', label: 'In Progress' },
    { key: 'review', label: 'Review' },
    { key: 'done', label: 'Done' }
  ];

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!formData.title.trim()) {
      toast.error('Task title is required');
      return;
    }

    if (!currentUser) {
      toast.error('You must be logged in to create tasks');
      return;
    }

    try {
      setIsSubmitting(true);

      const taskData = {
        title: formData.title.trim(),
        description: formData.description.trim(),
        status: formData.status,
        project_id: projectId,
        created_by: currentUser.id,
        assignee_id: formData.assignee || currentUser.id,
        due_date: formData.due_date,
        estimated_hours: formData.estimated_hours ? parseFloat(formData.estimated_hours) : null
      };

      const { data, error } = await supabase
        .from('tasks')
        .insert([taskData])
        .select()
        .single();

      if (error) throw error;

      toast.success('Task created successfully!');
      
      // Reset form
      setFormData({
        title: '',
        description: '',
        status: 'todo',
        assignee: '',
        due_date: null,
        estimated_hours: ''
      });

      // Notify parent component
      if (onTaskCreated) {
        onTaskCreated(data);
      }

      onClose();

    } catch (error) {
      console.error('Error creating task:', error);
      toast.error('Failed to create task. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };



  return (
    <Modal 
      isOpen={isOpen} 
      onClose={onClose}
      size="2xl"
      backdrop="blur"
      classNames={{
        backdrop: "bg-gradient-to-t from-zinc-900 to-zinc-900/10 backdrop-opacity-20",
        base: "border-[#292f46] bg-[#19172c] dark:bg-[#19172c] text-[#a8b0d3]",
        header: "border-b-[1px] border-[#292f46]",
        footer: "border-t-[1px] border-[#292f46]",
        closeButton: "hover:bg-white/5 active:bg-white/10",
      }}
    >
      <ModalContent>
        <form onSubmit={handleSubmit}>
          <ModalHeader className="flex flex-col gap-1">
            <div className="flex items-center gap-2">
              <div className="p-2 rounded-lg bg-green-500/20">
                <Plus className="w-5 h-5 text-green-400" />
              </div>
              <div>
                <h2 className="text-xl font-semibold text-white">Create New Task</h2>
                <p className="text-sm text-white/60">
                  {projectName ? `Add a new task to ${projectName}` : 'Add a new task to your project'}
                </p>
              </div>
            </div>
          </ModalHeader>

          <ModalBody className="py-6">
            <div className="space-y-6">
              {/* Task Title */}
              <Input
                label="Task Title"
                placeholder="Enter task title..."
                value={formData.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
                startContent={<FileText className="w-4 h-4 text-white/40" />}
                isRequired
                classNames={{
                  input: "text-white",
                  label: "text-white/70"
                }}
              />

              {/* Task Description */}
              <Textarea
                label="Description"
                placeholder="Describe the task in detail..."
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                minRows={3}
                classNames={{
                  input: "text-white",
                  label: "text-white/70"
                }}
              />

              {/* Status */}
              <Select
                  label="Status"
                  selectedKeys={[formData.status]}
                  onSelectionChange={(keys) => handleInputChange('status', Array.from(keys)[0])}
                  classNames={{
                    trigger: "bg-white/5",
                    label: "text-white/70"
                  }}
                >
                  {statusOptions.map((option) => (
                    <SelectItem key={option.key} value={option.key}>
                      {option.label}
                    </SelectItem>
                  ))}
                </Select>

              {/* Due Date and Estimated Hours */}
              <div className="grid grid-cols-2 gap-4">
                <Input
                  type="datetime-local"
                  label="Due Date"
                  value={formData.due_date || ''}
                  onChange={(e) => handleInputChange('due_date', e.target.value)}
                  startContent={<Calendar className="w-4 h-4 text-white/40" />}
                  classNames={{
                    input: "text-white",
                    label: "text-white/70"
                  }}
                />

                <Input
                  type="number"
                  label="Estimated Hours"
                  placeholder="0"
                  value={formData.estimated_hours}
                  onChange={(e) => handleInputChange('estimated_hours', e.target.value)}
                  min="0"
                  step="0.5"
                  classNames={{
                    input: "text-white",
                    label: "text-white/70"
                  }}
                />
              </div>


            </div>
          </ModalBody>

          <ModalFooter>
            <Button 
              color="danger" 
              variant="light" 
              onPress={onClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button 
              color="success" 
              type="submit"
              isLoading={isSubmitting}
              disabled={isSubmitting || !formData.title.trim()}
              startContent={!isSubmitting && <Plus className="w-4 h-4" />}
            >
              {isSubmitting ? 'Creating...' : 'Create Task'}
            </Button>
          </ModalFooter>
        </form>
      </ModalContent>
    </Modal>
  );
};

export default TaskCreationModal;
