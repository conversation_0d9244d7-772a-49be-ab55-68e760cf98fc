import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { resolve } from 'path'

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  mode: 'production',
  plugins: [
    react({
      // Use classic JSX runtime for better compatibility
      jsxRuntime: 'classic'
    })
  ],
  root: '.',
  build: {
    outDir: 'dist',
    emptyOutDir: true,
    sourcemap: false, // Disable sourcemaps in production for smaller bundles
    minify: 'esbuild', // Use esbuild for minification
    target: 'es2015', // Support modern browsers for smaller bundles
    rollupOptions: {
      input: {
        main: resolve(__dirname, 'index.html')
      },
      output: {
        // Disable manual chunks to avoid React JSX runtime issues
        manualChunks: undefined,
        // Optimize chunk file names
        chunkFileNames: (chunkInfo) => {
          const facadeModuleId = chunkInfo.facadeModuleId ? chunkInfo.facadeModuleId.split('/').pop().replace('.jsx', '').replace('.js', '') : 'chunk';
          return `js/${facadeModuleId}-[hash].js`;
        },
        assetFileNames: (assetInfo) => {
          const info = assetInfo.name.split('.');
          const ext = info[info.length - 1];
          if (/\.(png|jpe?g|svg|gif|tiff|bmp|ico)$/i.test(assetInfo.name)) {
            return `images/[name]-[hash].${ext}`;
          }
          if (/\.(css)$/i.test(assetInfo.name)) {
            return `css/[name]-[hash].${ext}`;
          }
          return `assets/[name]-[hash].${ext}`;
        }
      }
    },
    // Optimize chunk size warnings
    chunkSizeWarningLimit: 1000,
    // Enable CSS code splitting
    cssCodeSplit: true
  },
  server: {
    port: 5173,
    open: true
  },
  define: {
    global: 'globalThis',
    'process.env': {},
    'process.env.NODE_ENV': JSON.stringify('production'),
    // Force production mode
    __DEV__: false,
    'import.meta.env.DEV': false,
    'import.meta.env.PROD': true,
    'import.meta.env.MODE': JSON.stringify('production'),
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
      'buffer': 'buffer',
      // Fix HeroUI import issues
      'flat': resolve(__dirname, './src/utils/flat-patch.js'),
      'color': resolve(__dirname, './src/utils/color-patch.js'),
      'deepmerge': resolve(__dirname, './src/utils/deepmerge-patch.js'),
      // Fix tailwindcss/plugin import (with .js extension)
      'tailwindcss/plugin.js': resolve(__dirname, './src/utils/tailwind-plugin-patch.js'),
      'tailwindcss/plugin': resolve(__dirname, './src/utils/tailwind-plugin-patch.js')
    }
  },
  optimizeDeps: {
    include: [
      'buffer',
      'react',
      'react-dom',
      '@heroui/react',
      'framer-motion',
      'react-hot-toast',
      '@supabase/supabase-js',
      // Force optimization of problematic CommonJS packages
      'flat',
      'color',
      'deepmerge',
      'tailwindcss/plugin'
    ],
    // Exclude large dependencies that should be loaded on demand
    exclude: ['@heroui/theme']
  },
  // Performance optimizations
  esbuild: {
    // Keep console.log for debugging - DO NOT DROP CONSOLE MESSAGES
    // drop: [], // Disabled to preserve debug messages
    // Let the React plugin handle JSX transformation
    // jsx: 'automatic', // Commented out to avoid conflicts
    // Optimize for modern browsers
    target: 'es2020'
  }
}))
