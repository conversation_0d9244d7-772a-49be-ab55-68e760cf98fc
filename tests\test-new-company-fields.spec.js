import { test, expect } from '@playwright/test';

test.describe('New Company Fields Test', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to login page and authenticate
    await page.goto('https://royalty.technology/login');
    await page.waitForLoadState('networkidle');
    
    // Login with test credentials
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'TestPassword123!');
    await page.click('button[type="submit"]');
    
    // Wait for authentication to complete
    await page.waitForURL('**/dashboard', { timeout: 10000 });
    await page.waitForLoadState('networkidle');
  });

  test('Test new company fields in project wizard', async ({ page }) => {
    console.log('🔍 Testing new company fields in project wizard...');

    // Navigate to project creation
    await page.goto('https://royalty.technology/project/create');
    await page.waitForLoadState('networkidle');
    
    // Fill basic project information
    await page.fill('input[placeholder="Enter project name"]', 'Company Fields Test Project');
    await page.fill('textarea[placeholder="Describe your project"]', 'Test project for new company fields');
    
    // Look for the company information section
    const companySection = page.locator('text=Company Information').first();
    if (await companySection.isVisible()) {
      console.log('✓ Company Information section is visible');
      
      // Check for new fields
      const contactEmailField = page.locator('input[label="Contact Email"], input[placeholder*="contact email"], input[placeholder*="Contact Email"]');
      const signerNameField = page.locator('input[label="Authorized Signer Name"], input[placeholder*="signer"], input[placeholder*="Signer"]');
      const signerTitleField = page.locator('input[label="Signer Title"], input[placeholder*="title"]');
      const cityField = page.locator('input[label="City"], input[placeholder*="city"]');
      
      console.log('🔍 Checking for new fields...');
      
      if (await contactEmailField.isVisible()) {
        console.log('✓ Contact Email field is present');
        await contactEmailField.fill('<EMAIL>');
      } else {
        console.log('❌ Contact Email field not found');
      }
      
      if (await signerNameField.isVisible()) {
        console.log('✓ Authorized Signer Name field is present');
        await signerNameField.fill('John Doe');
      } else {
        console.log('❌ Authorized Signer Name field not found');
      }
      
      if (await signerTitleField.isVisible()) {
        console.log('✓ Signer Title field is present');
        await signerTitleField.fill('CEO');
      } else {
        console.log('❌ Signer Title field not found');
      }
      
      if (await cityField.isVisible()) {
        console.log('✓ City field is present');
        await cityField.fill('Test City');
      } else {
        console.log('❌ City field not found');
      }
      
      // Fill other required company fields
      const companyNameField = page.locator('input[label="Company Name"], input[placeholder*="company name"]');
      const companyAddressField = page.locator('input[label="Company Address"], input[placeholder*="company address"]');
      const stateField = page.locator('input[label="State/Province"], input[placeholder*="state"]');
      
      if (await companyNameField.isVisible()) {
        await companyNameField.fill('Test Company Inc.');
      }
      
      if (await companyAddressField.isVisible()) {
        await companyAddressField.fill('123 Test Street, Test City, Test State 12345');
      }
      
      if (await stateField.isVisible()) {
        await stateField.fill('Test State');
      }
    } else {
      console.log('❌ Company Information section not found');
    }
    
    // Navigate through wizard steps to reach Review & Agreement
    for (let i = 0; i < 6; i++) {
      await page.waitForTimeout(1000);
      
      const nextBtn = page.locator('button:has-text("Next"), button:has-text("Continue")').first();
      if (await nextBtn.isVisible()) {
        await nextBtn.click({ force: true });
        await page.waitForTimeout(1500);
      }
    }
    
    // Wait for Review & Agreement page to load
    await page.waitForTimeout(3000);
    
    // Check if we're on the Review & Agreement step
    const reviewTitle = page.locator('h2:has-text("Review & Agreement"), h1:has-text("Review & Agreement")');
    if (await reviewTitle.isVisible({ timeout: 10000 })) {
      console.log('✓ Reached Review & Agreement step');
      
      // Wait for agreement generation
      await page.waitForTimeout(10000);
      
      // Check agreement content
      const agreementContent = page.locator('.agreement-content, .agreement-preview, [class*="agreement"]').first();
      if (await agreementContent.isVisible()) {
        const content = await agreementContent.textContent();
        console.log('📄 Agreement Content Length:', content?.length || 0);
        console.log('📄 Agreement Content Preview:', content?.substring(0, 500) || 'No content');
        
        // Check if the agreement contains our test data
        if (content?.includes('Test Company Inc.')) {
          console.log('✅ Agreement contains company name');
        }
        if (content?.includes('<EMAIL>')) {
          console.log('✅ Agreement contains contact email');
        }
        if (content?.includes('John Doe')) {
          console.log('✅ Agreement contains signer name');
        }
        if (content?.includes('CEO')) {
          console.log('✅ Agreement contains signer title');
        }
        
        // Verify agreement is not minimal/empty
        if (content && content.length > 1000) {
          console.log('✅ Agreement appears to have substantial content');
        } else {
          console.log('⚠️ Agreement content appears minimal');
        }
      } else {
        console.log('❌ Agreement content not found');
      }
    } else {
      console.log('❌ Could not reach Review & Agreement step');
    }
    
    // Take a screenshot
    await page.screenshot({ path: 'company-fields-test.png', fullPage: true });
    console.log('📸 Screenshot saved as company-fields-test.png');
  });
});
