# Netlify CLI Deployment Script
# This script helps deploy your site with functions using the Netlify CLI
#
# Usage:
#   ./deploy-netlify-cli.ps1 [-RunBuild] [-SetEnvVars] [-SkipDeploy] [-DeployType <draft|prod>] [-NonInteractive]
#
# Parameters:
#   -RunBuild       : Run the build script before deploying (default: false)
#   -SetEnvVars     : Set Supabase environment variables (default: false)
#   -SkipDeploy     : Skip the deployment step (default: false)
#   -DeployType     : Type of deployment, either "draft" or "prod" (default: "prod")
#   -NonInteractive : Run in non-interactive mode, using all defaults (default: false)
#
# Examples:
#   ./deploy-netlify-cli.ps1                           # Interactive mode with prompts
#   ./deploy-netlify-cli.ps1 -RunBuild                 # Run build and then prompt for other options
#   ./deploy-netlify-cli.ps1 -NonInteractive          # Deploy to production with all defaults
#   ./deploy-netlify-cli.ps1 -RunBuild -NonInteractive # Build and deploy to production without prompts
#   ./deploy-netlify-cli.ps1 -DeployType draft         # Deploy as draft/preview

param (
    [switch]$SetEnvVars = $false,
    [switch]$RunBuild = $false,
    [switch]$SkipDeploy = $false,
    [ValidateSet("draft", "prod")]
    [string]$DeployType = "prod",
    [switch]$NonInteractive = $false
)

# Function to handle interactive or non-interactive mode
function Get-UserChoice {
    param (
        [string]$Prompt,
        [string]$DefaultValue
    )

    if ($NonInteractive) {
        return $DefaultValue
    } else {
        $response = Read-Host $Prompt
        if ([string]::IsNullOrEmpty($response)) {
            return $DefaultValue
        }
        return $response
    }
}

# Check if Netlify CLI is installed
Write-Host "Checking for Netlify CLI..." -ForegroundColor Cyan
$netlifyInstalled = $null
try {
    $netlifyInstalled = netlify --version
} catch {
    $netlifyInstalled = $null
}

if (-not $netlifyInstalled) {
    Write-Host "Netlify CLI not found. Installing..." -ForegroundColor Yellow
    npm install netlify-cli -g

    if ($LASTEXITCODE -ne 0) {
        Write-Host "Failed to install Netlify CLI. Please install it manually with 'npm install netlify-cli -g'" -ForegroundColor Red
        exit 1
    }

    Write-Host "Netlify CLI installed successfully!" -ForegroundColor Green
} else {
    Write-Host "Netlify CLI is already installed: $netlifyInstalled" -ForegroundColor Green
}

# Check if user is logged in to Netlify
Write-Host "Checking Netlify login status..." -ForegroundColor Cyan
$loginStatus = netlify status

if ($loginStatus -match "Not logged in") {
    Write-Host "You're not logged in to Netlify. Please log in:" -ForegroundColor Yellow
    netlify login

    if ($LASTEXITCODE -ne 0) {
        Write-Host "Failed to log in to Netlify. Please try again manually with 'netlify login'" -ForegroundColor Red
        exit 1
    }

    Write-Host "Successfully logged in to Netlify!" -ForegroundColor Green
} else {
    Write-Host "Already logged in to Netlify." -ForegroundColor Green
}

# Check if the site is linked
Write-Host "Checking if site is linked..." -ForegroundColor Cyan
$siteInfo = netlify status

if ($siteInfo -match "Not linked") {
    Write-Host "Site is not linked. Linking site..." -ForegroundColor Yellow
    netlify link

    if ($LASTEXITCODE -ne 0) {
        Write-Host "Failed to link site. Please try again manually with 'netlify link'" -ForegroundColor Red
        exit 1
    }

    Write-Host "Successfully linked site!" -ForegroundColor Green
} else {
    $siteMatch = $siteInfo | Select-String 'Site:\s+(.+)'
    if ($siteMatch) {
        Write-Host "Site is already linked: $($siteMatch.Matches[0].Groups[1].Value)" -ForegroundColor Green
    } else {
        Write-Host "Site is already linked" -ForegroundColor Green
    }
}

# Handle environment variables
if (-not $NonInteractive) {
    # Only ask in interactive mode if not specified via parameter
    if (-not $PSBoundParameters.ContainsKey('SetEnvVars')) {
        $setEnvVarsResponse = Get-UserChoice -Prompt "Do you want to set Supabase environment variables? (y/n) [n]" -DefaultValue "n"
        $SetEnvVars = $setEnvVarsResponse -eq "y"
    }
}

if ($SetEnvVars) {
    Write-Host "Enter your Supabase URL:" -ForegroundColor Cyan
    $supabaseUrl = Read-Host

    Write-Host "Enter your Supabase Service Key:" -ForegroundColor Cyan
    $supabaseServiceKey = Read-Host

    Write-Host "Enter your Supabase Anon Key:" -ForegroundColor Cyan
    $supabaseAnonKey = Read-Host

    Write-Host "Setting environment variables..." -ForegroundColor Yellow
    # Set both old and new format environment variables for compatibility
    netlify env:set SUPABASE_URL $supabaseUrl
    netlify env:set SUPABASE_SERVICE_KEY $supabaseServiceKey
    netlify env:set SUPABASE_ANON_KEY $supabaseAnonKey
    # Vite-specific environment variables (prefixed with VITE_)
    netlify env:set VITE_SUPABASE_URL $supabaseUrl
    netlify env:set VITE_SUPABASE_ANON_KEY $supabaseAnonKey

    Write-Host "Environment variables set successfully!" -ForegroundColor Green
}

# Handle build script
if (-not $NonInteractive) {
    # Only ask in interactive mode if not specified via parameter
    if (-not $PSBoundParameters.ContainsKey('RunBuild')) {
        $runBuildResponse = Get-UserChoice -Prompt "Do you want to run the build script before deploying? (y/n) [n]" -DefaultValue "n"
        $RunBuild = $runBuildResponse -eq "y"
    }
}

if ($RunBuild) {
    Write-Host "Running build script..." -ForegroundColor Yellow

    # Build the client application
    Push-Location client
    npm run build
    $buildExitCode = $LASTEXITCODE
    Pop-Location

    if ($buildExitCode -ne 0) {
        Write-Host "Build script failed. Please check for errors." -ForegroundColor Red
        exit 1
    }

    Write-Host "Build completed successfully!" -ForegroundColor Green
}

# Handle deployment
$shouldDeploy = -not $SkipDeploy

if (-not $NonInteractive -and -not $SkipDeploy) {
    $deployResponse = Get-UserChoice -Prompt "Do you want to deploy to Netlify now? (y/n) [y]" -DefaultValue "y"
    $shouldDeploy = $deployResponse -eq "y"
}

if ($shouldDeploy) {
    # Handle deployment type
    $isDraftDeploy = $DeployType -eq "draft"

    if (-not $NonInteractive -and -not $PSBoundParameters.ContainsKey('DeployType')) {
        Write-Host "Choose deployment type:" -ForegroundColor Cyan
        Write-Host "1. Draft deploy (preview)" -ForegroundColor Gray
        Write-Host "2. Production deploy" -ForegroundColor Gray
        $deployTypeResponse = Get-UserChoice -Prompt "" -DefaultValue "2"
        $isDraftDeploy = $deployTypeResponse -eq "1"
    }

    if ($isDraftDeploy) {
        Write-Host "Deploying draft preview..." -ForegroundColor Yellow
        Write-Host "Deploy path:        $PWD\client\dist" -ForegroundColor Gray
        Write-Host "Functions path:     $PWD\netlify\functions" -ForegroundColor Gray
        Write-Host "Configuration path: $PWD\netlify.toml" -ForegroundColor Gray
        netlify deploy --dir client/dist --functions netlify/functions
    } else {
        Write-Host "Deploying to production..." -ForegroundColor Yellow
        Write-Host "Deploy path:        $PWD\client\dist" -ForegroundColor Gray
        Write-Host "Functions path:     $PWD\netlify\functions" -ForegroundColor Gray
        Write-Host "Configuration path: $PWD\netlify.toml" -ForegroundColor Gray
        Write-Host "Deploying to main site URL..." -ForegroundColor Yellow
        netlify deploy --prod --dir client/dist --functions netlify/functions
    }

    if ($LASTEXITCODE -ne 0) {
        Write-Host "Deployment failed. Please check for errors." -ForegroundColor Red
        exit 1
    }

    Write-Host "Deployment completed successfully!" -ForegroundColor Green
}

Write-Host "Script completed!" -ForegroundColor Cyan
