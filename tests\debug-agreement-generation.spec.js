import { test, expect } from '@playwright/test';

const PRODUCTION_URL = 'https://royalty.technology';

test.describe('Debug Agreement Generation', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to login page and authenticate
    await page.goto('https://royalty.technology/login');
    await page.waitForLoadState('networkidle');

    // Login with test credentials
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'TestPassword123!');
    await page.click('button[type="submit"]');

    // Wait for authentication to complete
    await page.waitForURL('**/dashboard', { timeout: 10000 });
    await page.waitForLoadState('networkidle');
  });

  test('Debug agreement generation in browser console', async ({ page }) => {
    console.log('🔍 Starting agreement generation debug...');

    // Navigate to project creation
    await page.goto('https://royalty.technology/project/create');
    await page.waitForLoadState('networkidle');

    // Fill out basic project info
    await page.fill('input[placeholder="Enter project name"]', 'Debug Test Project');
    await page.fill('textarea[placeholder="Describe your project"]', 'A test project for debugging agreement generation');

    // Navigate through wizard steps to reach Review & Agreement
    for (let i = 0; i < 6; i++) {
      await page.waitForTimeout(1000);

      const nextBtn = page.locator('button:has-text("Next"), button:has-text("Continue")').first();
      if (await nextBtn.isVisible()) {
        await nextBtn.click({ force: true });
        await page.waitForTimeout(1500);
      }
    }

    // Wait for Review & Agreement step
    await page.waitForSelector('h2:has-text("Review & Agreement")', { timeout: 10000 });
    console.log('✓ Reached Review & Agreement step');

    // Listen to console logs to debug
    page.on('console', msg => {
      if (msg.type() === 'error') {
        console.log('❌ Browser Error:', msg.text());
      } else if (msg.text().includes('Agreement') || msg.text().includes('Template') || msg.text().includes('Generate')) {
        console.log('📝 Agreement Log:', msg.text());
      }
    });

    // Wait for agreement generation
    await page.waitForTimeout(5000);

    // Check agreement content
    const agreementContent = await page.locator('.agreement-content, .agreement-preview').first().textContent();
    console.log('📄 Agreement Content Length:', agreementContent?.length || 0);
    console.log('📄 Agreement Content Preview:', agreementContent?.substring(0, 200) || 'No content');

    // Try to regenerate
    const regenerateButton = page.locator('button:has-text("Regenerate")');
    if (await regenerateButton.isVisible()) {
      console.log('🔄 Clicking regenerate button...');
      await regenerateButton.click();
      await page.waitForTimeout(3000);

      const newContent = await page.locator('.agreement-content, .agreement-preview').first().textContent();
      console.log('📄 Regenerated Content Length:', newContent?.length || 0);
      console.log('📄 Regenerated Content Preview:', newContent?.substring(0, 200) || 'No content');
    }

    // Check if template is accessible
    const templateResponse = await page.request.get(`${PRODUCTION_URL}/contributor-agreement-template.md`);
    console.log('📋 Template Response Status:', templateResponse.status());
    
    if (templateResponse.ok()) {
      const templateContent = await templateResponse.text();
      console.log('📋 Template Content Length:', templateContent.length);
      console.log('📋 Template Preview:', templateContent.substring(0, 200));
    }

    // Take a screenshot for debugging
    await page.screenshot({ path: 'debug-agreement-generation.png', fullPage: true });
    console.log('📸 Screenshot saved as debug-agreement-generation.png');
  });
});
