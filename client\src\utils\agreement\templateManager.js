/**
 * Template Manager for Agreement Generator
 * 
 * This module provides functionality for managing different agreement templates.
 * It allows users to select between different templates based on project needs.
 */

/**
 * Template types available in the system
 */
export const TEMPLATE_TYPES = {
  STANDARD: 'standard',
  SIMPLIFIED: 'simplified',
  DETAILED: 'detailed'
};

/**
 * Template descriptions for UI display
 */
export const TEMPLATE_DESCRIPTIONS = {
  [TEMPLATE_TYPES.STANDARD]: 'Comprehensive agreement with all important legal sections. Includes advanced IP protection, revenue sharing, and restrictive covenants. Suitable for most professional projects.',
  [TEMPLATE_TYPES.SIMPLIFIED]: 'Simplified agreement with minimal legal language. Suitable for small projects or quick collaborations.',
  [TEMPLATE_TYPES.DETAILED]: 'Comprehensive agreement with all important legal sections. Includes advanced IP protection, revenue sharing, restrictive covenants, and optional provisions. Suitable for complex projects or high-value collaborations.'
};

/**
 * Template file paths
 */
const TEMPLATE_PATHS = {
  [TEMPLATE_TYPES.STANDARD]: '/templates/v2/comprehensive_contributor_agreement_template.md',
  [TEMPLATE_TYPES.SIMPLIFIED]: '/contributor-agreement-template.md', // Keep original for simplified version
  [TEMPLATE_TYPES.DETAILED]: '/templates/v2/comprehensive_contributor_agreement_template.md' // Use comprehensive template for detailed
};

/**
 * Template Manager class for handling agreement templates
 */
export class TemplateManager {
  constructor() {
    this.templates = {};
    this.defaultTemplate = TEMPLATE_TYPES.STANDARD;
  }

  /**
   * Load a template from the specified path
   * @param {string} templateType - The template type to load
   * @returns {Promise<string>} - The template text
   */
  async loadTemplate(templateType = this.defaultTemplate) {
    // If template is already loaded, return it
    if (this.templates[templateType]) {
      return this.templates[templateType];
    }

    // Get template path
    const templatePath = TEMPLATE_PATHS[templateType] || TEMPLATE_PATHS[this.defaultTemplate];

    try {
      // Fetch the template
      const response = await fetch(templatePath);
      if (!response.ok) {
        throw new Error(`Failed to load template: ${response.statusText}`);
      }

      const templateText = await response.text();
      
      // Cache the template
      this.templates[templateType] = templateText;
      
      return templateText;
    } catch (error) {
      console.error('Error loading template:', error);
      
      // If the requested template fails to load, try to load the default template
      if (templateType !== this.defaultTemplate) {
        console.warn(`Falling back to default template: ${this.defaultTemplate}`);
        return this.loadTemplate(this.defaultTemplate);
      }
      
      // If even the default template fails, return an empty string
      return '';
    }
  }

  /**
   * Get available template types
   * @returns {Array<string>} - Array of available template types
   */
  getAvailableTemplateTypes() {
    return Object.values(TEMPLATE_TYPES);
  }

  /**
   * Get template description
   * @param {string} templateType - The template type
   * @returns {string} - The template description
   */
  getTemplateDescription(templateType) {
    return TEMPLATE_DESCRIPTIONS[templateType] || 'No description available';
  }

  /**
   * Check if a template exists
   * @param {string} templateType - The template type to check
   * @returns {boolean} - Whether the template exists
   */
  templateExists(templateType) {
    return Object.values(TEMPLATE_TYPES).includes(templateType);
  }

  /**
   * Get the default template type
   * @returns {string} - The default template type
   */
  getDefaultTemplateType() {
    return this.defaultTemplate;
  }
}

// Create and export a singleton instance
export const templateManager = new TemplateManager();
