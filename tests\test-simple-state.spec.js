import { test, expect } from '@playwright/test';

test.describe('Simple State Check', () => {
  test('Quick check of page state', async ({ page }) => {
    console.log('🚀 Quick state check...');
    
    await page.goto('https://royalty.technology');
    await page.waitForTimeout(3000);
    
    // Check for loading spinner
    const hasLoadingSpinner = await page.locator('[data-testid="simple-loading"]').count();
    console.log('🔄 Loading Spinner Count:', hasLoadingSpinner);
    
    // Check for main content
    const hasMainContent = await page.locator('#main-content').count();
    console.log('📄 Main Content Count:', hasMainContent);
    
    // Check for navigation
    const hasNavigation = await page.locator('nav').count();
    console.log('🧭 Navigation Count:', hasNavigation);
    
    // Check debug flags
    const debugFlags = await page.evaluate(() => ({
      jsExecuting: !!window.JAVASCRIPT_EXECUTING,
      mainJsxExecuted: !!window.MAIN_JSX_EXECUTED,
      titleModified: document.title.includes('MAIN.JSX EXECUTED')
    }));
    
    console.log('🔍 Debug Flags:', debugFlags);
    
    // Check if React is working by looking for React-specific attributes
    const reactElements = await page.evaluate(() => {
      const elements = document.querySelectorAll('[data-overlay-container], [class*="flex"], [class*="sr-only"]');
      return elements.length;
    });
    
    console.log('⚛️ React-style Elements:', reactElements);
    
    // Simple visual check - is the page showing content?
    const pageText = await page.textContent('body');
    const hasContent = pageText && pageText.trim().length > 0;
    console.log('📝 Has Page Content:', hasContent);
    console.log('📝 Page Text Length:', pageText ? pageText.length : 0);
    
    if (pageText && pageText.length < 500) {
      console.log('📝 Page Text Sample:', pageText.substring(0, 200));
    }
    
    expect(true).toBeTruthy();
  });
});
