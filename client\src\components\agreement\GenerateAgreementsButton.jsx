import React, { useState } from 'react';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';
import { generateAgreement } from '../../utils/agreement';

/**
 * Button component for project admins to generate agreements for all contributors
 * who don't have agreements yet
 */
const GenerateAgreementsButton = ({ projectId, contributors, onComplete }) => {
  const [loading, setLoading] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);

  // Generate agreements for all contributors who don't have one
  const generateAgreements = async () => {
    if (!projectId || !contributors || contributors.length === 0) {
      toast.error('Missing required information');
      return;
    }

    setLoading(true);

    try {
      // Get agreement template
      const agreementTemplate = await fetch('/contributor-agreement-template.md').then(res => res.text());

      // Format date for the agreement header
      const today = new Date();
      const month = today.toLocaleDateString('en-US', { month: 'long' });
      const day = today.getDate();
      const year = today.getFullYear().toString();
      const headerDate = `${month} ${day}, ${year}`;

      // Find project owner
      const owner = contributors.find(c => c.permission_level === 'Owner');
      const ownerName = owner?.display_name || owner?.users?.display_name || 'Project Owner';

      // Get project details
      const { data: project, error: projectError } = await supabase
        .from('projects')
        .select('*')
        .eq('id', projectId)
        .single();

      if (projectError) throw projectError;

      // For each contributor, check if they have an agreement
      let agreementsCreated = 0;
      let agreementsSkipped = 0;

      for (const contributor of contributors) {
        // Check if contributor already has an agreement
        const { data: existingAgreements, error: checkError } = await supabase
          .from('contributor_agreements')
          .select('id')
          .eq('project_id', projectId)
          .eq('contributor_id', contributor.id);

        if (checkError) {
          console.error(`Error checking agreement for contributor ${contributor.id}:`, checkError);
          continue;
        }

        // Skip if agreement already exists
        if (existingAgreements && existingAgreements.length > 0) {
          agreementsSkipped++;
          continue;
        }

        // Generate agreement text
        const contributorName = contributor.display_name || contributor.users?.display_name || contributor.email || 'Contributor';

        // Get project milestones for the exhibits
        let milestones = [];
        try {
          // Fetch from milestones table
          const { data: projectMilestones, error: milestonesError } = await supabase
            .from('milestones')
            .select('*')
            .eq('project_id', projectId)
            .order('due_date', { ascending: true });

          if (!milestonesError && projectMilestones && projectMilestones.length > 0) {
            milestones = projectMilestones;
          }
        } catch (milestonesError) {
          console.warn('Error fetching milestones:', milestonesError);
        }

        // Check if milestones exist and log a message (don't use toast)
        if (!milestones || milestones.length === 0) {
          console.log('No milestones found for this project. A generic roadmap will be included in the agreements.');
        }

        // Get royalty model for the project
        let royaltyModel = null;
        try {
          const { data, error } = await supabase
            .from('royalty_models')
            .select('*')
            .eq('project_id', projectId)
            .maybeSingle();

          if (!error && data) {
            royaltyModel = data;
          } else if (error && error.code !== 'PGRST116') {
            console.warn('Error fetching royalty model:', error);
          }
        } catch (modelError) {
          console.warn('Exception fetching royalty model:', modelError);
          // Continue without royalty model data
        }

        // Use the new agreement generator to create a fully customized agreement
        let agreementText;
        try {
          // Prepare options for the agreement generator
          const options = {
            contributors: contributors,
            currentUser: {
              email: contributor.email,
              user_metadata: { full_name: contributorName }
            },
            royaltyModel: royaltyModel,
            milestones: milestones,
            fullName: contributorName
          };

          // Generate the customized agreement using our new generator
          agreementText = await generateAgreement(project, options);

          console.log('Agreement generated successfully with new agreement generator');
        } catch (error) {
          console.error('Error generating agreement:', error);

          // Fallback to basic template replacement if the generator fails
          agreementText = agreementTemplate
            // Basic replacements
            .replace(/\[Project Name\]/g, project.name)
            .replace(/\[Date\]/g, headerDate)
            .replace(/\[Project Owner\]/g, ownerName)
            .replace(/\[Contributor\]/g, contributorName)
            .replace(/Village of The Ages/g, project.name)
            .replace(/village simulation game where players guide communities through historical progressions and manage resource-based challenges/g, project.description || 'A collaborative project')
            .replace(/City of Gamers Inc\./gi, project.company_name || ownerName || 'Project Owner')
            .replace(/City of Gamers/gi, project.company_name || ownerName || 'Project Owner')
            .replace(/\bCOG\b/gi, project.company_name || ownerName || 'Project Owner')
            .replace(/Gynell Journigan/gi, ownerName)
            .replace(/Florida/gi, 'the applicable jurisdiction')
            .replace(/Orlando/gi, 'the applicable jurisdiction')
            .replace(/1205 43rd Street, Suite B, Orlando, Florida 32839/gi, '[Project Address]')
            .replace(/billing@cogfuture\.com/gi, '[Project Owner Email]');

          toast.error('Error generating agreement. Using basic template instead.');
        }

        // Verify that we're not losing any content from the template
        if (agreementText.length < agreementTemplate.length * 0.7) {
          console.error('Warning: Generated agreement appears to be missing content from the template');
        }

        // Create agreement
        const { error: createError } = await supabase
          .from('contributor_agreements')
          .insert({
            project_id: projectId,
            contributor_id: contributor.id,
            agreement_text: agreementText,
            version: 1,
            status: 'pending',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          });

        if (createError) {
          console.error(`Error creating agreement for contributor ${contributor.id}:`, createError);
          continue;
        }

        agreementsCreated++;
      }

      // Show success message
      if (agreementsCreated > 0) {
        toast.success(`Created ${agreementsCreated} new agreements`);
      } else if (agreementsSkipped === contributors.length) {
        toast.info('All contributors already have agreements');
      } else {
        toast.info(`No new agreements created. ${agreementsSkipped} contributors already have agreements.`);
      }

      // Call the onComplete callback if provided
      if (onComplete) {
        onComplete();
      }

    } catch (error) {
      console.error('Error generating agreements:', error);
      toast.error('Failed to generate agreements');
    } finally {
      setLoading(false);
      setShowConfirmation(false);
    }
  };

  return (
    <div className="generate-agreements-container">
      {!showConfirmation ? (
        <button
          className="generate-agreements-button"
          onClick={() => setShowConfirmation(true)}
          disabled={loading}
        >
          <i className="bi bi-file-earmark-plus"></i> New Agreements
        </button>
      ) : (
        <div className="confirmation-dialog">
          <p>This will create agreements for all contributors who don't have one yet. Continue?</p>
          <div className="confirmation-actions">
            <button
              className="confirm-button"
              onClick={generateAgreements}
              disabled={loading}
            >
              {loading ? 'Generating...' : 'Yes, Generate Agreements'}
            </button>
            <button
              className="cancel-button"
              onClick={() => setShowConfirmation(false)}
              disabled={loading}
            >
              Cancel
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default GenerateAgreementsButton;
