/* Minimal base styles - let Hero<PERSON> handle everything else */
:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  overflow: hidden; /* Hide body scrollbar for immersive navigation */
}

/* Ensure html doesn't create scrollbars */
html {
  overflow: hidden;
  height: 100%;
  width: 100%;
}

/* Content scrolling styles */
.content-scrollable {
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-width: thin; /* Show thin scrollbars in content areas */
  scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
}

.content-scrollable::-webkit-scrollbar {
  width: 6px;
}

.content-scrollable::-webkit-scrollbar-track {
  background: transparent;
}

.content-scrollable::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.content-scrollable::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* Navigation areas - hide scrollbars completely */
.navigation-area {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.navigation-area::-webkit-scrollbar {
  display: none;
}

/* Allow scrolling within canvas content when in content mode */
.canvas-content {
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
}

.canvas-content::-webkit-scrollbar {
  width: 6px;
}

.canvas-content::-webkit-scrollbar-track {
  background: transparent;
}

.canvas-content::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.canvas-content::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

.pointer {
  cursor: pointer;
}


