import React, { useState, useEffect } from 'react';
import { Card, CardBody, CardHeader, Button, Chip, Input, Select, SelectItem, Modal, ModalContent, ModalHeader, ModalBody, ModalFooter, Textarea, Badge } from '@heroui/react';
import { motion } from 'framer-motion';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';

/**
 * Content Moderation Component - Content Review and Moderation Queue
 * 
 * Features:
 * - Content moderation queue with pending reviews
 * - Automated content filtering and flagging
 * - Community guidelines enforcement
 * - Appeal management and resolution
 * - Content action history and audit trail
 */
const ContentModeration = ({ currentUser, className = "" }) => {
  const [loading, setLoading] = useState(true);
  const [moderationQueue, setModerationQueue] = useState([]);
  const [filterType, setFilterType] = useState('all');
  const [filterStatus, setFilterStatus] = useState('pending');
  const [selectedItem, setSelectedItem] = useState(null);
  const [showReviewModal, setShowReviewModal] = useState(false);
  const [reviewAction, setReviewAction] = useState('');
  const [reviewNotes, setReviewNotes] = useState('');

  // Load moderation queue
  const loadModerationQueue = async () => {
    try {
      setLoading(true);
      
      const { data: { session } } = await supabase.auth.getSession();
      const authToken = session?.access_token;
      
      if (!authToken) {
        toast.error('Authentication required');
        return;
      }

      // Fetch real moderation queue data from Supabase
      const { data: moderationData, error: moderationError } = await supabase
        .from('content_moderation')
        .select(`
          *,
          author:users!content_moderation_author_id_fkey(
            id,
            display_name,
            email,
            reputation
          ),
          reporter:users!content_moderation_reported_by_fkey(
            id,
            display_name,
            email
          )
        `)
        .order('created_at', { ascending: false })
        .limit(100);

      if (moderationError) throw moderationError;

      // Transform data to match component expectations
      const transformedQueue = moderationData?.map(item => ({
        id: item.id,
        contentType: item.content_type,
        contentId: item.content_id,
        title: item.title || `${item.content_type} Content`,
        flaggedBy: item.flagged_by,
        flagReason: item.flag_reason,
        flagDetails: item.flag_details,
        content: item.content_preview || item.content,
        reportedBy: item.reporter?.email || 'system',
        status: item.status,
        priority: item.priority,
        createdAt: item.created_at,
        reviewedAt: item.reviewed_at,
        reviewedBy: item.reviewed_by,
        action: item.action,
        reviewNotes: item.review_notes,
        author: {
          name: item.author?.display_name || 'Unknown User',
          email: item.author?.email || '<EMAIL>',
          reputation: item.author?.reputation || 0
        }
      })) || [];

      setModerationQueue(transformedQueue);
      
    } catch (error) {
      console.error('Error loading moderation queue:', error);
      toast.error('Failed to load moderation queue');
    } finally {
      setLoading(false);
    }
  };

  // Filter moderation queue
  const filteredQueue = moderationQueue.filter(item => {
    const matchesType = filterType === 'all' || item.contentType === filterType;
    const matchesStatus = filterStatus === 'all' || item.status === filterStatus;
    return matchesType && matchesStatus;
  });

  // Get priority color
  const getPriorityColor = (priority) => {
    const colors = {
      'critical': 'danger',
      'high': 'warning',
      'medium': 'primary',
      'low': 'default'
    };
    return colors[priority] || 'default';
  };

  // Get status color
  const getStatusColor = (status) => {
    const colors = {
      'pending': 'warning',
      'reviewed': 'success',
      'appealed': 'secondary',
      'escalated': 'danger'
    };
    return colors[status] || 'default';
  };

  // Get flag reason display
  const getFlagReasonDisplay = (reason) => {
    const reasons = {
      'inappropriate_language': 'Inappropriate Language',
      'spam_links': 'Spam Links',
      'misleading_information': 'Misleading Information',
      'harassment': 'Harassment',
      'potential_scam': 'Potential Scam',
      'copyright_violation': 'Copyright Violation',
      'hate_speech': 'Hate Speech',
      'violence': 'Violence/Threats'
    };
    return reasons[reason] || reason;
  };

  // Handle review action
  const handleReviewAction = (item, action) => {
    setSelectedItem(item);
    setReviewAction(action);
    setReviewNotes('');
    setShowReviewModal(true);
  };

  // Execute review action
  const executeReviewAction = async () => {
    try {
      if (!reviewNotes.trim()) {
        toast.error('Please provide review notes');
        return;
      }

      // Mock API call - in production this would call the moderation API
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Update item status locally
      setModerationQueue(prevQueue => 
        prevQueue.map(item => 
          item.id === selectedItem.id 
            ? { 
                ...item, 
                status: 'reviewed',
                action: reviewAction,
                reviewNotes: reviewNotes,
                reviewedAt: new Date().toISOString(),
                reviewedBy: currentUser?.display_name || 'Admin'
              }
            : item
        )
      );

      toast.success(`Content ${reviewAction} successfully`);
      setShowReviewModal(false);
      setSelectedItem(null);
      setReviewAction('');
      setReviewNotes('');
      
    } catch (error) {
      console.error('Error executing review action:', error);
      toast.error('Failed to execute action');
    }
  };

  // Handle contact user
  const handleContactUser = (item) => {
    toast.info(`Message interface for ${item.author.name} coming soon`);
  };

  // Format timestamp
  const formatTimestamp = (timestamp) => {
    return new Date(timestamp).toLocaleString();
  };

  useEffect(() => {
    loadModerationQueue();
  }, []);

  if (loading) {
    return (
      <div className={`flex items-center justify-center h-64 ${className}`}>
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <Card className="bg-gradient-to-r from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-3">
              <span className="text-3xl">🔍</span>
              <div>
                <h2 className="text-2xl font-bold">Content Moderation</h2>
                <p className="text-default-600">Review and moderate flagged content</p>
              </div>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold text-warning">
                {filteredQueue.filter(item => item.status === 'pending').length}
              </div>
              <div className="text-sm text-default-600">Pending Reviews</div>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Filters */}
      <Card>
        <CardBody className="p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <Select
              label="Content Type"
              selectedKeys={[filterType]}
              onSelectionChange={(keys) => setFilterType(Array.from(keys)[0])}
              className="w-48"
            >
              <SelectItem key="all">All Types</SelectItem>
              <SelectItem key="project">Projects</SelectItem>
              <SelectItem key="profile">Profiles</SelectItem>
              <SelectItem key="studio">Studios</SelectItem>
              <SelectItem key="comment">Comments</SelectItem>
            </Select>
            
            <Select
              label="Status"
              selectedKeys={[filterStatus]}
              onSelectionChange={(keys) => setFilterStatus(Array.from(keys)[0])}
              className="w-48"
            >
              <SelectItem key="all">All Status</SelectItem>
              <SelectItem key="pending">Pending</SelectItem>
              <SelectItem key="reviewed">Reviewed</SelectItem>
              <SelectItem key="appealed">Appealed</SelectItem>
              <SelectItem key="escalated">Escalated</SelectItem>
            </Select>
            
            <Button
              color="primary"
              variant="flat"
              onClick={loadModerationQueue}
              startContent={<span>🔄</span>}
            >
              Refresh
            </Button>
          </div>
        </CardBody>
      </Card>

      {/* Moderation Queue */}
      <div className="space-y-4">
        {filteredQueue.length === 0 ? (
          <Card>
            <CardBody className="text-center py-8">
              <div className="text-default-500">No items found for the selected filters</div>
            </CardBody>
          </Card>
        ) : (
          filteredQueue.map((item, index) => (
            <motion.div
              key={item.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <Card className="hover:shadow-lg transition-shadow">
                <CardBody className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <Badge
                        color={getPriorityColor(item.priority)}
                        content={item.priority}
                        size="sm"
                      >
                        <span className="text-2xl">🚩</span>
                      </Badge>
                      <div>
                        <h3 className="font-semibold text-lg">{item.title}</h3>
                        <div className="flex items-center gap-2 text-sm text-default-600">
                          <Chip size="sm" variant="flat">{item.contentType}</Chip>
                          <span>•</span>
                          <span>Flagged by: {item.flaggedBy}</span>
                          <span>•</span>
                          <span>{formatTimestamp(item.createdAt)}</span>
                        </div>
                      </div>
                    </div>
                    <Chip
                      color={getStatusColor(item.status)}
                      variant="flat"
                      size="sm"
                    >
                      {item.status}
                    </Chip>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <label className="text-sm font-medium text-default-700">Reason</label>
                      <div className="text-sm text-default-600">
                        {getFlagReasonDisplay(item.flagReason)}
                      </div>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-default-700">Reported By</label>
                      <div className="text-sm text-default-600">{item.reportedBy}</div>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-default-700">Author</label>
                      <div className="text-sm text-default-600">
                        {item.author.name} ({item.author.email})
                        <br />
                        Reputation: {item.author.reputation}⭐
                      </div>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-default-700">Details</label>
                      <div className="text-sm text-default-600">{item.flagDetails}</div>
                    </div>
                  </div>

                  <div className="mb-4">
                    <label className="text-sm font-medium text-default-700">Flagged Content</label>
                    <div className="mt-1 p-3 bg-default-100 rounded-lg text-sm">
                      {item.content}
                    </div>
                  </div>

                  {item.status === 'reviewed' && (
                    <div className="mb-4 p-3 bg-success-50 rounded-lg">
                      <div className="text-sm font-medium text-success-700">Review Completed</div>
                      <div className="text-sm text-success-600">
                        Action: {item.action} • By: {item.reviewedBy} • {formatTimestamp(item.reviewedAt)}
                      </div>
                      <div className="text-sm text-success-600 mt-1">
                        Notes: {item.reviewNotes}
                      </div>
                    </div>
                  )}

                  {item.status === 'pending' && (
                    <div className="flex items-center gap-2">
                      <Button
                        size="sm"
                        color="success"
                        variant="flat"
                        onClick={() => handleReviewAction(item, 'approved')}
                      >
                        Approve
                      </Button>
                      <Button
                        size="sm"
                        color="warning"
                        variant="flat"
                        onClick={() => handleReviewAction(item, 'edited')}
                      >
                        Edit
                      </Button>
                      <Button
                        size="sm"
                        color="danger"
                        variant="flat"
                        onClick={() => handleReviewAction(item, 'removed')}
                      >
                        Remove
                      </Button>
                      <Button
                        size="sm"
                        color="primary"
                        variant="flat"
                        onClick={() => handleContactUser(item)}
                      >
                        Contact User
                      </Button>
                    </div>
                  )}
                </CardBody>
              </Card>
            </motion.div>
          ))
        )}
      </div>

      {/* Review Action Modal */}
      <Modal 
        isOpen={showReviewModal} 
        onClose={() => setShowReviewModal(false)}
        size="lg"
      >
        <ModalContent>
          <ModalHeader>
            <h3>Review Action: {reviewAction}</h3>
          </ModalHeader>
          <ModalBody>
            <div className="space-y-4">
              <div>
                <p>You are about to <strong>{reviewAction}</strong> the following content:</p>
                <div className="mt-2 p-3 bg-default-100 rounded-lg text-sm">
                  {selectedItem?.content}
                </div>
              </div>
              
              <Textarea
                label="Review Notes (required)"
                placeholder="Please provide detailed notes about your decision..."
                value={reviewNotes}
                onChange={(e) => setReviewNotes(e.target.value)}
                minRows={4}
              />
            </div>
          </ModalBody>
          <ModalFooter>
            <Button color="danger" variant="flat" onPress={() => setShowReviewModal(false)}>
              Cancel
            </Button>
            <Button 
              color="primary" 
              onPress={executeReviewAction}
              disabled={!reviewNotes.trim()}
            >
              Confirm {reviewAction}
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
};

export default ContentModeration;
