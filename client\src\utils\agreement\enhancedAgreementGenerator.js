/**
 * Enhanced Agreement Generator for Royaltea
 *
 * A complete rewrite of the agreement generation system that properly handles
 * project-specific content and ensures all VOTA-specific content is replaced.
 */

import { templateManager, TEMPLATE_TYPES } from './templateManager';

/**
 * Enhanced Agreement Generator class
 */
export class EnhancedAgreementGenerator {
  constructor() {
    // Today's date as default
    const today = new Date();
    this.currentDate = today.toLocaleDateString('en-US', {
      month: 'long',
      day: 'numeric',
      year: 'numeric'
    });
  }

  /**
   * Load a template based on template type
   * @param {string} templateType - The template type to load
   * @returns {Promise<string>} - The template text
   */
  async loadTemplate(templateType = TEMPLATE_TYPES.STANDARD) {
    return templateManager.loadTemplate(templateType);
  }

  /**
   * Generate a complete agreement based on template and project data
   * Updated to use the new variable-based system (100% accurate)
   * @param {string} templateText - The agreement template text
   * @param {Object} data - The data object containing project, user, and other information
   * @param {Object} options - Additional options
   * @returns {string} - The customized agreement
   */
  async generateAgreement(templateText, data, options = {}) {
    if (!templateText || !data || !data.project) {
      throw new Error('Template text and project data are required');
    }

    try {
      // Import the new variable-based generator
      const { NewAgreementGenerator } = await import('./newAgreementGenerator.js');
      const generator = new NewAgreementGenerator();

      // Convert the data format to what the new generator expects
      const projectData = this._convertDataToProject(data);
      const contributorData = this._convertDataToContributor(data, options);

      // Use the new 100% accurate variable-based system
      const agreement = await generator.generateAgreement(
        templateText,
        projectData,
        contributorData
      );

      console.log('EnhancedAgreementGenerator: Using new variable-based system (100% accurate)');
      return agreement;

    } catch (error) {
      console.error('EnhancedAgreementGenerator: Error with new system, falling back to legacy:', error);

      // Fallback to legacy system if new system fails
      return this._generateLegacyAgreement(templateText, data, options);
    }
  }

  /**
   * Legacy agreement generation (kept for fallback)
   */
  _generateLegacyAgreement(templateText, data, options = {}) {
    const { project, currentUser, royaltyModel, milestones = [] } = data;
    const { agreementDate = null } = options;

    // Set the agreement date if provided
    if (agreementDate) {
      const date = new Date(agreementDate);
      this.currentDate = date.toLocaleDateString('en-US', {
        month: 'long',
        day: 'numeric',
        year: 'numeric'
      });
    }

    // Extract owner information
    const owner = this._extractOwnerInfo(data);

    // Extract contributor information
    const contributor = this._extractContributorInfo(data, currentUser);

    // Process the template with all replacements
    let processedAgreement = this._processTemplate(
      templateText,
      project,
      owner,
      contributor,
      royaltyModel,
      milestones
    );

    return processedAgreement;
  }

  /**
   * Convert data format to project format for new generator
   */
  _convertDataToProject(data) {
    const { project } = data;

    return {
      name: project.name,
      description: project.description,
      projectType: project.projectType || project.project_type || 'software',
      features: project.features || 'Advanced features to be implemented',
      coreFeatures: project.coreFeatures || this._generateDefaultFeatures(project),
      technicalRequirements: project.technicalRequirements || this._generateDefaultTechRequirements(project),
      roadmapPhases: project.roadmapPhases || this._generateDefaultRoadmap(project),
      milestones: project.milestones || this._generateDefaultMilestones(project),

      // Company information - use actual project data
      company_name: project.company_name,
      address: project.company_address,
      contact_email: project.contact_email,
      city: project.company_city,
      state: project.company_state,
      zip: project.company_zip,
      county: project.company_county,
      signer_name: project.signer_name,
      signer_title: project.signer_title,
      legal_entity_type: project.legal_entity_type || 'corporation',
      incorporation_state: project.incorporation_state || project.company_state
    };
  }

  /**
   * Convert data format to contributor format for new generator
   */
  _convertDataToContributor(data, options) {
    const { currentUser } = data;
    const fullName = data.fullName || currentUser?.user_metadata?.full_name || currentUser?.email || '[Contributor Name]';

    return {
      contributors: [{
        id: currentUser?.id || 'contributor_1',
        email: currentUser?.email || '<EMAIL>',
        name: fullName,
        address: data.address || '[Contributor Address]',
        state: 'State',
        county: 'County'
      }],
      currentUser: {
        id: currentUser?.id || 'contributor_1',
        email: currentUser?.email || '<EMAIL>',
        user_metadata: {
          full_name: fullName
        }
      },
      fullName: fullName,
      agreementDate: options.agreementDate ? new Date(options.agreementDate) : new Date()
    };
  }

  /**
   * Generate default features for project
   */
  _generateDefaultFeatures(project) {
    const projectType = project.projectType || project.project_type || 'software';

    switch (projectType.toLowerCase()) {
      case 'game':
        return `**Core Game Features:**
- Engaging gameplay mechanics
- Interactive user interface
- Performance optimization
- Quality assurance testing`;
      case 'music':
        return `**Core Music Features:**
- Professional audio production
- Sound design and mixing
- Creative composition
- Audio engineering`;
      case 'software':
      case 'app':
        return `**Core Software Features:**
- User-friendly interface design
- Robust backend functionality
- Performance optimization
- Quality assurance testing`;
      default:
        return `**Core Project Features:**
- Professional development standards
- Quality deliverables
- Performance optimization
- Comprehensive testing`;
    }
  }

  /**
   * Generate default technical requirements
   */
  _generateDefaultTechRequirements(project) {
    const projectType = project.projectType || project.project_type || 'software';

    return `**Technical Requirements:**
- Industry-standard development practices
- Quality code and documentation
- Testing and validation procedures
- Performance optimization for ${projectType} projects`;
  }

  /**
   * Generate default roadmap
   */
  _generateDefaultRoadmap(project) {
    const projectType = project.projectType || project.project_type || 'software';

    return `**Development Phases:**
**Phase 1: Planning and Setup**
- Project planning and requirements analysis
- Technical architecture design
- Development environment setup

**Phase 2: Core Development**
- Core ${projectType} functionality implementation
- Testing and quality assurance
- Documentation creation

**Phase 3: Finalization**
- Final testing and optimization
- Deployment preparation
- Project completion and handover`;
  }

  /**
   * Generate default milestones
   */
  _generateDefaultMilestones(project) {
    const projectType = project.projectType || project.project_type || 'software';

    return [
      {
        title: 'Project Initiation',
        description: 'Project setup and planning completion',
        dueDate: 'Month 1'
      },
      {
        title: 'Core Development',
        description: `${projectType} core functionality implementation`,
        dueDate: 'Month 2-3'
      },
      {
        title: 'Project Completion',
        description: 'Final testing, optimization, and delivery',
        dueDate: 'Month 4'
      }
    ];
  }

  /**
   * Extract owner information from the data
   * @param {Object} data - The data object
   * @returns {Object} - Owner information
   */
  _extractOwnerInfo(data) {
    const { project, user } = data;
    const contributors = data.contributors || [];

    // Find the owner in the contributors list
    const ownerContributor = contributors.find(c => c.permission_level === 'Owner');

    return {
      name: ownerContributor?.display_name || user?.owner?.name || project.company_name || 'Project Owner',
      email: ownerContributor?.email || user?.owner?.email || project.contact_email || '<EMAIL>',
      company: project.company_name || ownerContributor?.display_name || 'Project Owner',
      address: project.address || '1209 N Orange St, Wilmington, DE 19801',
      city: project.city || 'Wilmington',
      state: project.state || 'Delaware',
      zip: project.zip || '19801'
    };
  }

  /**
   * Extract contributor information from the data
   * @param {Object} data - The data object
   * @param {Object} currentUser - The current user
   * @returns {Object} - Contributor information
   */
  _extractContributorInfo(data, currentUser) {
    const fullName = data.fullName || currentUser?.user_metadata?.full_name || currentUser?.email || 'Contributor';

    return {
      name: fullName,
      email: currentUser?.email || '<EMAIL>',
      isCompany: false, // Default to individual
      address: data.address || ''
    };
  }

  /**
   * Process the template with all replacements
   * @param {string} template - The agreement template
   * @param {Object} project - The project data
   * @param {Object} owner - The owner information
   * @param {Object} contributor - The contributor information
   * @param {Object} royaltyModel - The royalty model
   * @param {Array} milestones - The project milestones
   * @returns {string} - The processed agreement
   */
  _processTemplate(template, project, owner, contributor, royaltyModel, milestones) {
    console.log('EnhancedAgreementGenerator: Processing template');
    console.log('EnhancedAgreementGenerator: Project type:', project.projectType);

    // Validate inputs
    if (!template) {
      console.error('EnhancedAgreementGenerator: Template is empty or undefined');
      throw new Error('Template is required');
    }

    if (!project) {
      console.error('EnhancedAgreementGenerator: Project data is empty or undefined');
      throw new Error('Project data is required');
    }

    // Start with the original template
    let processed = template;

    try {
      // Replace basic information
      console.log('EnhancedAgreementGenerator: Replacing basic information');
      processed = this._replaceBasicInfo(processed, project, owner, contributor);

      // Replace project type specific terminology
      console.log('EnhancedAgreementGenerator: Replacing project type terminology');
      processed = this._replaceProjectTypeTerminology(processed, project);

      // Generate and replace exhibits
      console.log('EnhancedAgreementGenerator: Generating Exhibit I');
      const exhibitI = this._generateExhibitI(project);
      console.log('EnhancedAgreementGenerator: Generating Exhibit II');
      const exhibitII = this._generateExhibitII(project, milestones);
      console.log('EnhancedAgreementGenerator: Replacing exhibits');
      processed = this._replaceExhibits(processed, exhibitI, exhibitII);

      // Generate and replace Schedule A
      console.log('EnhancedAgreementGenerator: Generating Schedule A');
      const scheduleA = this._generateScheduleA(project);
      console.log('EnhancedAgreementGenerator: Replacing Schedule A');
      processed = this._replaceScheduleA(processed, scheduleA);

      // Replace royalty model information
      console.log('EnhancedAgreementGenerator: Replacing royalty model information');
      processed = this._replaceRoyaltyModelInfo(processed, royaltyModel, project);

      // Final cleanup to catch any remaining placeholders
      console.log('EnhancedAgreementGenerator: Performing final cleanup');
      processed = this._finalCleanup(processed, project, owner, contributor);

      // Add title and introduction at the very end to ensure they're not removed
      console.log('EnhancedAgreementGenerator: Adding title and introduction');
      processed = this._addTitleAndIntroduction(processed, owner, contributor, project);

      console.log('EnhancedAgreementGenerator: Template processing complete');
    } catch (error) {
      console.error('EnhancedAgreementGenerator: Error processing template:', error);
      throw error;
    }

    return processed;
  }

  /**
   * Add title and introduction to the agreement
   * @param {string} template - The processed template
   * @param {Object} owner - The owner information
   * @param {Object} contributor - The contributor information
   * @param {Object} project - The project data
   * @returns {string} - The template with title and introduction
   */
  _addTitleAndIntroduction(template, owner, contributor, project) {
    let processed = template;

    // Remove any existing title or introduction to avoid duplication
    if (processed.startsWith('# ')) {
      // Find the end of the title section (after the second #)
      const titleEndIndex = processed.indexOf('\n\n', processed.indexOf('\n', processed.indexOf('#')));
      if (titleEndIndex !== -1) {
        processed = processed.substring(titleEndIndex + 2);
      }
    }

    // Remove any existing introduction paragraph
    if (processed.includes('This Contributor Agreement (this "Agreement") is effective as of')) {
      const introStartIndex = processed.indexOf('This Contributor Agreement');
      const introEndIndex = processed.indexOf('\n\n', introStartIndex);
      if (introStartIndex !== -1 && introEndIndex !== -1) {
        processed = processed.substring(0, introStartIndex) + processed.substring(introEndIndex + 2);
      }
    }

    // Add title with project name first, then company name, then CONTRIBUTOR AGREEMENT
    const projectName = project?.name || 'Project';
    const companyName = owner.company || '[COMPANY NAME]';
    let result = `# ${projectName}\n`;

    // Add company name if available
    if (companyName && companyName !== '[COMPANY NAME]') {
      result += `# ${companyName}\n`;
    }

    // Add CONTRIBUTOR AGREEMENT
    result += `# CONTRIBUTOR AGREEMENT\n\n`;

    // Add introduction paragraph
    const companyAddress = owner.address || '[ADDRESS]';
    const contributorName = contributor.name || '[CONTRIBUTOR NAME]';
    result += `This Contributor Agreement (this "Agreement") is effective as of [ ], 20[__], by and between ${companyName}, with its principal place of business at ${companyAddress} (the "Company") and ${contributorName} (the "Contributor").\n\n`;

    // Add the rest of the template
    // Check if we need to add the intro before Recitals
    if (processed.trim().startsWith('Recitals')) {
      result += `## Recitals\n\n${processed.substring(processed.indexOf('Recitals') + 'Recitals'.length).trim()}`;
    } else {
      result += processed;
    }

    return result;
  }

  /**
   * Replace basic information in the template
   * @param {string} template - The agreement template
   * @param {Object} project - The project data
   * @param {Object} owner - The owner information
   * @param {Object} contributor - The contributor information
   * @returns {string} - The processed template
   */
  _replaceBasicInfo(template, project, owner, contributor) {
    let processed = template;

    // Replace date information with current date
    const today = new Date();
    const formattedDate = today.toLocaleDateString('en-US', {
      month: 'long',
      day: 'numeric',
      year: 'numeric'
    });

    // Only replace date placeholders if we have a valid date
    if (owner.useCurrentDate) {
      processed = processed.replace(/\[ \], 20\[__\]/g, formattedDate);
      processed = processed.replace(/\[Date\]/g, formattedDate);
      processed = processed.replace(/Effective Date: .+?\n/g, `Effective Date: ${formattedDate}\n`);
      processed = processed.replace(/THIS CONTRIBUTOR AGREEMENT \(this "Agreement"\) is made as of .+? by and between/g,
                    `THIS CONTRIBUTOR AGREEMENT (this "Agreement") is made as of ${formattedDate} by and between`);
      processed = processed.replace(/This Contributor Agreement \(this "Agreement"\) is effective as of .+? by and between/g,
                    `This Contributor Agreement (this "Agreement") is effective as of ${formattedDate} by and between`);
    } else {
      // Keep the placeholder format for manual completion
      processed = processed.replace(/\[Date\]/g, '[ ], 20[__]');
      processed = processed.replace(/Effective Date: .+?\n/g, `Effective Date: [ ], 20[__]\n`);
    }

    // Replace project information
    processed = processed.replace(/\[Project Name\]/g, project.name);
    processed = processed.replace(/Village of The Ages/g, project.name);
    processed = processed.replace(/village simulation game where players guide communities through historical progressions and manage resource-based challenges/g, project.description);

    // Replace owner information only if available
    if (owner.name) {
      processed = processed.replace(/\[Project Owner\]/g, owner.name);
      processed = processed.replace(/Gynell Journigan/gi, owner.name);
    }

    // Replace company information only if available
    if (owner.company) {
      processed = processed.replace(/City of Gamers Inc\./gi, owner.company);
      processed = processed.replace(/City of Gamers/gi, owner.company);
      processed = processed.replace(/\bCOG\b/gi, owner.company);
    }

    // Replace location information
    processed = processed.replace(/Florida/gi, owner.state || '[State]');
    processed = processed.replace(/Orlando/gi, owner.city || '[City]');
    processed = processed.replace(/1205 43rd Street, Suite B, Orlando, Florida 32839/gi, owner.address || '[Address]');
    processed = processed.replace(/the applicable jurisdiction/gi, owner.state || '[State]');

    // Replace county references only if we have city/state data
    if (owner.city && owner.state) {
      // Try to determine the county name based on the city/state
      let countyName = '';
      if (owner.city.toLowerCase() === 'wilmington' && owner.state.toLowerCase() === 'delaware') {
        countyName = 'NEW CASTLE COUNTY';
      } else {
        // If we don't have a specific mapping, use the city name with COUNTY
        countyName = owner.city.toUpperCase() + ' COUNTY';
      }
      processed = processed.replace(/ORANGE COUNTY/gi, countyName);
      processed = processed.replace(/Orange County/gi, countyName.charAt(0) + countyName.slice(1).toLowerCase());
    }

    // Replace contact information
    // Make sure to replace all instances of personal email addresses with company email
    const emailReplacements = [
      /billing@cogfuture\.com/gi,
      /thesolmarauder@gmail\.com/gi,
      /solmarauder@gmail\.com/gi,
      /thesolmarauder@/gi,
      /\[Project Owner Email\]/gi,
      /\[Company Email\]/gi,
      /\[Email\]/gi
    ];

    emailReplacements.forEach(pattern => {
      processed = processed.replace(pattern, owner.email || '[Company Email]');
    });

    // Replace contributor information only if available
    if (contributor.name) {
      processed = processed.replace(/\[Contributor\]/g, contributor.name);
      processed = processed.replace(/\[_+\]/g, contributor.name);
      processed = processed.replace(/\[Contributor Name\]/g, contributor.name);
      processed = processed.replace(/\[CONTRIBUTOR NAME\]/g, contributor.name.toUpperCase());
    }

    return processed;
  }
  /**
   * Replace project type specific terminology
   * @param {string} template - The agreement template
   * @param {Object} project - The project data
   * @returns {string} - The processed template
   */
  _replaceProjectTypeTerminology(template, project) {
    console.log('EnhancedAgreementGenerator: Replacing project type terminology');

    if (!template) {
      console.error('EnhancedAgreementGenerator: Template is empty or undefined in _replaceProjectTypeTerminology');
      return '';
    }

    const projectType = project.projectType || 'project';
    console.log('EnhancedAgreementGenerator: Project type for terminology replacement:', projectType);

    let processed = template;

    try {
      // Get replacements for this project type
      const replacements = this._getProjectTypeReplacements(projectType);
      console.log('EnhancedAgreementGenerator: Got replacements for project type:', projectType);

      // Apply specific VOTA replacements first
      console.log('EnhancedAgreementGenerator: Applying VOTA-specific replacements');
      processed = processed.replace(/Village of The Ages/g, project.name || 'Project');
      processed = processed.replace(/village simulation game/g, projectType === 'game' ? 'game' : projectType);
      processed = processed.replace(/village simulation/g, projectType === 'game' ? 'game' : projectType);
      processed = processed.replace(/players guide communities through historical progressions/g,
        projectType === 'game' ? 'players engage with interactive content' :
        projectType === 'music' ? 'listeners experience audio content' :
        projectType === 'software' || projectType === 'app' ? 'users interact with the application' :
        projectType === 'film' || projectType === 'video' ? 'viewers experience visual content' :
        'users engage with the project');
      processed = processed.replace(/manage resource-based challenges/g,
        projectType === 'game' ? 'overcome various challenges' :
        projectType === 'music' ? 'experience different musical elements' :
        projectType === 'software' || projectType === 'app' ? 'utilize various features' :
        projectType === 'film' || projectType === 'video' ? 'experience the narrative' :
        'engage with project elements');

      // Fix specific terminology issues
      processed = processed.replace(/video projects/gi,
        projectType === 'game' ? 'video games' :
        projectType === 'music' ? 'music projects' :
        projectType === 'software' || projectType === 'app' ? 'software projects' :
        projectType === 'film' || projectType === 'video' ? 'video projects' :
        'projects');

      // Fix specific platform references
      processed = processed.replace(/Epic projects Store/gi, 'distribution platform');
      processed = processed.replace(/Epic Games Store/gi, 'distribution platform');

      // Fix redundant content references
      processed = processed.replace(/additional content content/gi, 'additional content');

      // Fix more game-specific terminology
      if (projectType !== 'game') {
        processed = processed.replace(/game development/gi,
          projectType === 'music' ? 'music production' :
          projectType === 'software' || projectType === 'app' ? 'software development' :
          projectType === 'film' || projectType === 'video' ? 'film production' :
          'project development');

        processed = processed.replace(/game design/gi,
          projectType === 'music' ? 'music composition' :
          projectType === 'software' || projectType === 'app' ? 'software design' :
          projectType === 'film' || projectType === 'video' ? 'film direction' :
          'project design');
      }

      // Apply word-by-word replacements with word boundaries
      console.log('EnhancedAgreementGenerator: Applying word-by-word replacements');
      Object.entries(replacements).forEach(([search, replace]) => {
        // Skip empty search terms
        if (!search.trim()) return;

        // Create regex with word boundary
        const regex = new RegExp(`\\b${this._escapeRegExp(search)}\\b`, 'gi');
        const before = processed;
        processed = processed.replace(regex, replace);

        // Log if replacements were made
        if (before !== processed) {
          console.log(`EnhancedAgreementGenerator: Replaced "${search}" with "${replace}"`);
        }

        // Also try with capitalized first letter
        const capitalizedSearch = search.charAt(0).toUpperCase() + search.slice(1);
        const capitalizedRegex = new RegExp(`\\b${this._escapeRegExp(capitalizedSearch)}\\b`, 'g');
        const capitalizedReplace = replace.charAt(0).toUpperCase() + replace.slice(1);
        const beforeCap = processed;
        processed = processed.replace(capitalizedRegex, capitalizedReplace);

        // Log if capitalized replacements were made
        if (beforeCap !== processed) {
          console.log(`EnhancedAgreementGenerator: Replaced "${capitalizedSearch}" with "${capitalizedReplace}"`);
        }
      });

      // Apply phrase replacements (without word boundaries)
      console.log('EnhancedAgreementGenerator: Applying phrase replacements');
      const phraseReplacements = this._getProjectTypePhraseReplacements(projectType);

      phraseReplacements.forEach(({ search, replace }) => {
        // Skip empty search terms
        if (!search.trim()) return;

        const phraseRegex = new RegExp(this._escapeRegExp(search), 'gi');
        const before = processed;
        processed = processed.replace(phraseRegex, replace);

        // Log if replacements were made
        if (before !== processed) {
          console.log(`EnhancedAgreementGenerator: Replaced phrase "${search}" with "${replace}"`);
        }
      });

      // Replace revenue definition section
      console.log('EnhancedAgreementGenerator: Replacing revenue definition');
      processed = this._replaceRevenueDefinition(processed, projectType);

      // Check if we still have game-specific content
      if (projectType !== 'game' && processed.includes('game')) {
        console.warn('EnhancedAgreementGenerator: Agreement still contains "game" references after replacement');
      }

    } catch (error) {
      console.error('EnhancedAgreementGenerator: Error replacing project type terminology:', error);
      // Return the original template if there's an error
      return template;
    }

    return processed;
  }

  /**
   * Escape special characters for use in a regular expression
   * @param {string} string - The string to escape
   * @returns {string} - The escaped string
   */
  _escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  /**
   * Get project type specific terminology replacements
   * @param {string} projectType - The project type
   * @returns {Object} - Map of search terms to replacements
   */
  _getProjectTypeReplacements(projectType) {
    // Default replacements (game-specific terms to generic terms)
    const defaultReplacements = {
      'game': 'project',
      'games': 'projects',
      'player': 'user',
      'players': 'users',
      'gameplay': 'usage',
      'level': 'component',
      'levels': 'components',
      'character': 'element',
      'characters': 'elements',
      'village simulation': 'digital project',
      'historical progressions': 'project objectives',
      'DLC': 'additional content',
      'DLC content': 'additional content',
      'game build': 'project version',
      'game builds': 'project versions',
      'Steam': 'distribution platform',
      'Epic Games Store': 'distribution platform',
      'console platform': 'distribution platform',
      'early access': 'initial release'
    };

    // Project type specific replacements
    switch (projectType) {
      case 'music':
        return {
          'game': 'music project',
          'games': 'music projects',
          'player': 'listener',
          'players': 'listeners',
          'gameplay': 'listening experience',
          'level': 'track',
          'levels': 'tracks',
          'character': 'artist',
          'characters': 'artists',
          'village simulation': 'music production',
          'historical progressions': 'musical development',
          'DLC': 'additional release',
          'DLC content': 'additional releases',
          'game build': 'music release',
          'game builds': 'music releases',
          'Steam': 'Spotify',
          'Epic Games Store': 'Apple Music',
          'console platform': 'streaming platform',
          'early access': 'initial release'
        };
      case 'software':
      case 'app':
        return {
          'game': 'software application',
          'games': 'software applications',
          'player': 'user',
          'players': 'users',
          'gameplay': 'user experience',
          'level': 'module',
          'levels': 'modules',
          'character': 'component',
          'characters': 'components',
          'village simulation': 'software development',
          'historical progressions': 'software iterations',
          'DLC': 'update',
          'DLC content': 'updates and expansions',
          'game build': 'software release',
          'game builds': 'software releases',
          'Steam': 'app store',
          'Epic Games Store': 'Google Play',
          'console platform': 'platform',
          'early access': 'beta release'
        };
      case 'film':
      case 'video':
        return {
          'game': 'film project',
          'games': 'film projects',
          'player': 'viewer',
          'players': 'viewers',
          'gameplay': 'viewing experience',
          'level': 'scene',
          'levels': 'scenes',
          'character': 'actor',
          'characters': 'actors',
          'village simulation': 'film production',
          'historical progressions': 'narrative development',
          'DLC': 'additional content',
          'DLC content': 'additional content',
          'game build': 'film cut',
          'game builds': 'film cuts',
          'Steam': 'streaming service',
          'Epic Games Store': 'distribution platform',
          'console platform': 'streaming platform',
          'early access': 'preview release'
        };
      default:
        return defaultReplacements;
    }
  }

  /**
   * Get project type specific phrase replacements
   * @param {string} projectType - The project type
   * @returns {Array} - Array of {search, replace} objects
   */
  _getProjectTypePhraseReplacements(projectType) {
    // Default phrase replacements
    const defaultPhrases = [
      { search: 'game ready', replace: 'project ready' },
      { search: 'game world', replace: 'project environment' },
      { search: 'player experience', replace: 'user experience' },
      { search: 'gameplay mechanics', replace: 'core functionality' },
      { search: 'game development', replace: 'project development' },
      { search: 'village simulation game', replace: 'digital project' },
      { search: 'resource-based challenges', replace: 'project challenges' },
      { search: 'historical eras', replace: 'project phases' },
      { search: 'village layout', replace: 'project structure' },
      { search: 'building system', replace: 'component system' },
      { search: 'resource gathering', replace: 'resource management' },
      { search: 'AI for villagers', replace: 'automated systems' },
      { search: 'weather and disaster systems', replace: 'environmental systems' },
      { search: 'trading mechanics', replace: 'exchange systems' },
      { search: 'technology progression', replace: 'advancement system' },
      { search: 'video project development', replace: 'video game development' }
    ];

    // Project type specific phrase replacements
    switch (projectType) {
      case 'music':
        return [
          { search: 'game ready', replace: 'release ready' },
          { search: 'game world', replace: 'musical landscape' },
          { search: 'player experience', replace: 'listener experience' },
          { search: 'gameplay mechanics', replace: 'musical elements' },
          { search: 'game development', replace: 'music production' },
          { search: 'village simulation game', replace: 'music project' },
          { search: 'resource-based challenges', replace: 'production challenges' },
          { search: 'historical eras', replace: 'musical movements' },
          { search: 'village layout', replace: 'track arrangement' },
          { search: 'building system', replace: 'composition system' },
          { search: 'resource gathering', replace: 'sound collection' },
          { search: 'AI for villagers', replace: 'automated arrangements' },
          { search: 'weather and disaster systems', replace: 'dynamic audio elements' },
          { search: 'trading mechanics', replace: 'collaboration features' },
          { search: 'technology progression', replace: 'production advancement' }
        ];
      case 'software':
      case 'app':
        return [
          { search: 'game ready', replace: 'deployment ready' },
          { search: 'game world', replace: 'application environment' },
          { search: 'player experience', replace: 'user experience' },
          { search: 'gameplay mechanics', replace: 'core functionality' },
          { search: 'game development', replace: 'software development' },
          { search: 'village simulation game', replace: 'software application' },
          { search: 'resource-based challenges', replace: 'technical challenges' },
          { search: 'historical eras', replace: 'development phases' },
          { search: 'village layout', replace: 'application architecture' },
          { search: 'building system', replace: 'component system' },
          { search: 'resource gathering', replace: 'data collection' },
          { search: 'AI for villagers', replace: 'automated processes' },
          { search: 'weather and disaster systems', replace: 'error handling systems' },
          { search: 'trading mechanics', replace: 'data exchange protocols' },
          { search: 'technology progression', replace: 'feature advancement' }
        ];
      case 'film':
      case 'video':
        return [
          { search: 'game ready', replace: 'screening ready' },
          { search: 'game world', replace: 'film setting' },
          { search: 'player experience', replace: 'viewer experience' },
          { search: 'gameplay mechanics', replace: 'storytelling techniques' },
          { search: 'game development', replace: 'film production' },
          { search: 'village simulation game', replace: 'film project' },
          { search: 'resource-based challenges', replace: 'production challenges' },
          { search: 'historical eras', replace: 'narrative periods' },
          { search: 'village layout', replace: 'set design' },
          { search: 'building system', replace: 'production system' },
          { search: 'resource gathering', replace: 'asset collection' },
          { search: 'AI for villagers', replace: 'background characters' },
          { search: 'weather and disaster systems', replace: 'special effects systems' },
          { search: 'trading mechanics', replace: 'character interactions' },
          { search: 'technology progression', replace: 'plot development' }
        ];
      default:
        return defaultPhrases;
    }
  }

  /**
   * Replace Revenue Definition and Calculation section based on project type
   * @param {string} template - The agreement template
   * @param {string} projectType - The project type
   * @returns {string} - The processed template
   */
  _replaceRevenueDefinition(template, projectType) {
    let processed = template;

    // Define platform fees replacement based on project type
    let platformFeesReplacement = '';
    switch (projectType) {
      case 'software':
      case 'app':
        platformFeesReplacement = 'app store commissions, payment processor fees';
        break;
      case 'music':
        platformFeesReplacement = 'streaming service fees, distribution costs';
        break;
      case 'film':
      case 'video':
        platformFeesReplacement = 'streaming platform fees, distribution costs';
        break;
      default:
        platformFeesReplacement = 'distribution platform fees';
    }

    // Replace platform fees in Revenue Definition section
    processed = processed.replace(
      /Platform fees \(e\.g\., Steam, Epic Games Store, console platform fees\)/g,
      `Platform fees (e.g., ${platformFeesReplacement})`
    );

    return processed;
  }
  /**
   * Generate Exhibit I (Specifications)
   * @param {Object} project - The project data
   * @returns {string} - The Exhibit I content
   */
  _generateExhibitI(project) {
    const projectName = project.name || 'Project';
    const projectDescription = project.description || 'A collaborative project';
    const projectType = project.projectType || 'project';

    // Generate project-specific content based on project type
    const coreFeatures = this._generateCoreFeatures(projectType);
    const technicalRequirements = this._generateTechnicalRequirements(projectType, project);

    // Create a project type specific overview
    let overviewExtension = '';
    switch (projectType) {
      case 'game':
        overviewExtension = 'The project features engaging gameplay mechanics and progression systems designed to create a compelling player experience.';
        break;
      case 'music':
        overviewExtension = 'The project encompasses composition, recording, and production designed to create a compelling listening experience.';
        break;
      case 'software':
      case 'app':
        overviewExtension = 'The project features intuitive user interfaces and robust functionality designed to create a seamless user experience.';
        break;
      case 'film':
      case 'video':
        overviewExtension = 'The project encompasses compelling visual storytelling and production values designed to create an engaging viewing experience.';
        break;
      default:
        overviewExtension = 'The project is designed to meet specific goals and requirements through collaborative development.';
    }

    return `**${projectName} - Project Specifications**

**Project Overview:**
${projectDescription}. ${overviewExtension}

**Core Features:**

${coreFeatures}

**Technical Requirements:**

${technicalRequirements}`;
  }

  /**
   * Generate core features based on project type
   * @param {string} projectType - The project type
   * @returns {string} - The core features content
   */
  _generateCoreFeatures(projectType) {
    switch (projectType) {
      case 'game':
        return `1. **Core Gameplay Mechanics**
   - Primary gameplay systems
   - Player interaction model
   - Challenge and progression systems
   - Resource management

2. **Game World & Environment**
   - World design and structure
   - Environmental systems
   - Interactive elements
   - Visual style and atmosphere

3. **Player Experience**
   - Character development
   - Skill progression
   - Reward systems
   - Engagement mechanics

4. **Technical Features**
   - Performance optimization
   - Cross-platform compatibility
   - Multiplayer functionality (if applicable)
   - Save system and persistence`;
      case 'music':
        return `1. **Composition & Arrangement**
   - Melodic development
   - Harmonic structure
   - Rhythmic elements
   - Arrangement and orchestration

2. **Production Quality**
   - Recording techniques
   - Mixing standards
   - Mastering specifications
   - Sound design elements

3. **Distribution Format**
   - Digital streaming requirements
   - Physical media specifications
   - Metadata standards
   - Promotional materials

4. **Rights Management**
   - Licensing structure
   - Royalty tracking
   - Usage permissions
   - Attribution requirements`;
      case 'software':
      case 'app':
        return `1. **Core Functionality**
   - User authentication and management
   - Data processing capabilities
   - Integration with external services
   - Performance optimization

2. **User Interface**
   - Responsive design
   - Accessibility features
   - Navigation structure
   - Visual consistency

3. **Data Management**
   - Storage architecture
   - Backup and recovery
   - Security protocols
   - Data validation

4. **Deployment & Maintenance**
   - Deployment pipeline
   - Update mechanisms
   - Monitoring systems
   - Support infrastructure`;
      case 'film':
      case 'video':
        return `1. **Narrative & Storytelling**
   - Story structure
   - Character development
   - Dialogue and scripting
   - Pacing and flow

2. **Visual Production**
   - Cinematography approach
   - Lighting design
   - Set and location requirements
   - Visual effects planning

3. **Audio Production**
   - Sound design
   - Music and scoring
   - Dialogue recording
   - Audio mixing standards

4. **Post-Production**
   - Editing workflow
   - Color grading
   - Special effects integration
   - Final delivery formats`;
      default:
        return `1. **Core Components**
   - Primary functionality
   - Essential features
   - Basic structure
   - Fundamental elements

2. **User Experience**
   - Interface design
   - Interaction patterns
   - Accessibility considerations
   - User flow optimization

3. **Technical Framework**
   - Architecture design
   - Component integration
   - Performance standards
   - Security measures

4. **Delivery & Support**
   - Deployment strategy
   - Maintenance plan
   - Update mechanism
   - Support structure`;
    }
  }

  /**
   * Generate technical requirements based on project type
   * @param {string} projectType - The project type
   * @param {Object} project - The project data
   * @returns {string} - The technical requirements content
   */
  _generateTechnicalRequirements(projectType, project) {
    // Extract platform and technology information
    const platform = project.platform || this._getDefaultPlatform(projectType);
    const technologies = project.technologies || this._getDefaultTechnologies(projectType);
    const compatibility = project.compatibility || this._getDefaultCompatibility(projectType);

    switch (projectType) {
      case 'game':
        return `- Platform: ${platform}
- Technologies: ${technologies}
- Minimum Specs: Standard hardware requirements for the target platforms
- Art Style: Stylized, readable visuals with distinctive aesthetics
- Audio: Atmospheric soundtrack with appropriate sound design
- Version Control: Git-based source control with proper branching strategy`;
      case 'music':
        return `- Format: High-quality WAV and MP3 deliverables
- Sample Rate: 48kHz/24-bit minimum
- Channels: Stereo with optional surround mixes
- Production Tools: ${technologies}
- Metadata: Complete ID3 tags and ISRC codes
- Stems: Individual track stems for licensing flexibility`;
      case 'software':
      case 'app':
        return `- Platform: ${platform}
- Technologies: ${technologies}
- Database: ${project.database || 'SQL/NoSQL as appropriate for the application needs'}
- APIs: RESTful with GraphQL options
- Security: OAuth 2.0, data encryption, and regular security audits
- CI/CD: Automated testing and deployment pipeline`;
      case 'film':
      case 'video':
        return `- Platform: ${platform}
- Technologies: ${technologies}
- Resolution: 4K (3840x2160) minimum
- Format: H.264/H.265 encoding
- Audio: 48kHz/24-bit stereo audio
- Color: Rec. 709 color space with proper color grading`;
      default:
        return `- Platform: ${platform}
- Technologies: ${technologies}
- Compatibility: ${compatibility}
- Standards: Industry standard best practices
- Integration: Compatible with required third-party services
- Documentation: Comprehensive technical documentation`;
    }
  }

  /**
   * Get default platform based on project type
   * @param {string} projectType - The project type
   * @returns {string} - Default platform
   */
  _getDefaultPlatform(projectType) {
    switch (projectType) {
      case 'game':
        return 'PC, Mobile, Consoles';
      case 'music':
        return 'Digital streaming platforms, Physical media';
      case 'software':
      case 'app':
        return 'Web, Mobile, Desktop';
      case 'film':
      case 'video':
        return 'Streaming platforms, Digital distribution';
      default:
        return 'Web, Mobile, Desktop';
    }
  }

  /**
   * Get default technologies based on project type
   * @param {string} projectType - The project type
   * @returns {string} - Default technologies
   */
  _getDefaultTechnologies(projectType) {
    switch (projectType) {
      case 'game':
        return 'Unity/Unreal Engine, C#/C++, 3D modeling software';
      case 'music':
        return 'Pro Tools, Logic Pro, Ableton Live';
      case 'software':
      case 'app':
        return 'React, Node.js, SQL/NoSQL databases';
      case 'film':
      case 'video':
        return 'Adobe Premiere Pro, After Effects, DaVinci Resolve';
      default:
        return 'To be determined based on project needs';
    }
  }

  /**
   * Get default compatibility based on project type
   * @param {string} projectType - The project type
   * @returns {string} - Default compatibility
   */
  _getDefaultCompatibility(projectType) {
    switch (projectType) {
      case 'game':
        return 'Windows, macOS, iOS, Android';
      case 'music':
        return 'All major streaming platforms and physical media formats';
      case 'software':
      case 'app':
        return 'Cross-browser, responsive design for all screen sizes';
      case 'film':
      case 'video':
        return 'All major streaming platforms and digital distribution channels';
      default:
        return 'Standard compatibility with industry platforms';
    }
  }
  /**
   * Generate Exhibit II (Product Roadmap)
   * @param {Object} project - The project data
   * @param {Array} milestones - The project milestones
   * @returns {string} - The Exhibit II content
   */
  _generateExhibitII(project, milestones = []) {
    const projectName = project.name || 'Project';
    const projectType = project.projectType || 'project';

    // Generate development phases based on project type
    const developmentPhases = this._generateDevelopmentPhases(projectType, project);

    // Generate milestones section
    const milestonesSection = this._generateMilestonesSection(projectType, milestones, project);

    return `**${projectName} - Development Roadmap**

${developmentPhases}
${milestonesSection}`;
  }

  /**
   * Generate development phases based on project type
   * @param {string} projectType - The project type
   * @param {Object} project - The project data
   * @returns {string} - The development phases content
   */
  _generateDevelopmentPhases(projectType, project) {
    // Determine project length and duration
    const projectLength = project.projectLength || 'medium';
    const durationMonths = project.estimatedDuration || 4;

    switch (projectType) {
      case 'game':
        return `**Phase 1: Core Development (Month 1)**
- Basic game systems implementation
- Core mechanics development
- Initial asset creation
- Prototype development

**Phase 2: Feature Implementation (Month 2)**
- Complete feature set implementation
- Asset integration
- UI/UX development
- Testing and iteration

**Phase 3: Polish and Refinement (Month 3)**
- Visual polish
- Performance optimization
- Gameplay balancing
- Bug fixing

**Phase 4: Finalization (Month 4)**
- Final testing
- Platform integration
- Marketing preparation
- Release readiness

`;
      case 'music':
        return `**Phase 1: Pre-Production (Month 1)**
- Concept development
- Reference track analysis
- Arrangement planning
- Demo recording

**Phase 2: Production (Month 2)**
- Recording sessions
- Instrument tracking
- Vocal recording
- Initial mixing

**Phase 3: Post-Production (Month 3)**
- Final mixing
- Mastering
- Metadata preparation
- Distribution setup

**Phase 4: Release (Month 4)**
- Marketing materials
- Platform submission
- Promotional strategy
- Release execution

`;
      case 'software':
      case 'app':
        return `**Phase 1: Planning & Design (Month 1)**
- Requirements gathering
- Architecture design
- UI/UX wireframing
- Database schema design

**Phase 2: Development (Month 2)**
- Core functionality implementation
- Frontend development
- Backend systems
- API integration

**Phase 3: Testing & Refinement (Month 3)**
- Quality assurance
- User acceptance testing
- Performance optimization
- Security auditing

**Phase 4: Deployment (Month 4)**
- Final testing
- Documentation
- Deployment preparation
- Launch and monitoring

`;
      case 'film':
      case 'video':
        return `**Phase 1: Pre-Production (Month 1)**
- Script finalization
- Storyboarding
- Location scouting
- Casting and crew assembly

**Phase 2: Production (Month 2)**
- Principal photography
- Sound recording
- B-roll footage
- Asset collection

**Phase 3: Post-Production (Month 3)**
- Editing
- Visual effects
- Sound design
- Color grading

**Phase 4: Distribution (Month 4)**
- Final cut approval
- Format conversion
- Platform submission
- Marketing and promotion

`;
      default:
        return `**Phase 1: Planning (Month 1)**
- Requirements gathering
- Concept development
- Resource allocation
- Initial design

**Phase 2: Development (Month 2)**
- Core implementation
- Feature development
- Integration
- Initial testing

**Phase 3: Refinement (Month 3)**
- Quality assurance
- Performance optimization
- User feedback incorporation
- Final adjustments

**Phase 4: Completion (Month 4)**
- Final testing
- Documentation
- Delivery preparation
- Launch readiness

`;
    }
  }

  /**
   * Generate milestones section based on provided milestones or defaults
   * @param {string} projectType - The project type
   * @param {Array} milestones - The project milestones
   * @param {Object} project - The project data
   * @returns {string} - The milestones section content
   */
  _generateMilestonesSection(projectType, milestones, project) {
    let milestonesText = '**Milestones:**\n\n';

    // If milestones are provided, use them
    if (milestones && milestones.length > 0) {
      milestones.forEach((milestone, index) => {
        const dueDate = milestone.due_date ? new Date(milestone.due_date).toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        }) : milestone.deadline || 'To be determined';

        milestonesText += `**${index + 1}. ${milestone.title || milestone.name || `Milestone ${index + 1}`}** (${dueDate})\n`;

        // Add milestone description or tasks
        if (milestone.description || milestone.tasks) {
          const tasks = Array.isArray(milestone.tasks) ?
            milestone.tasks :
            (milestone.description ? [milestone.description] : ['To be determined']);

          tasks.forEach(task => {
            milestonesText += `   - ${task}\n`;
          });
        } else {
          milestonesText += '   - To be determined\n';
        }

        milestonesText += '\n';
      });
    } else {
      // Generate default milestones based on project type
      switch (projectType) {
        case 'game':
          milestonesText += `**1. First Playable** (End of Month 1)
   - Core mechanics implemented
   - Basic systems functional
   - Initial gameplay loop playable

**2. Alpha Version** (End of Month 2)
   - All core features implemented
   - Placeholder assets integrated
   - Basic UI implemented

**3. Beta Version** (Mid-Month 3)
   - All features complete
   - Final assets integrated
   - Performance optimized

**4. Release Candidate** (End of Month 4)
   - All known bugs fixed
   - Platform requirements met
   - Ready for distribution

`;
          break;
        case 'music':
          milestonesText += `**1. Demo Completion** (End of Month 1)
   - Basic arrangement completed
   - Demo tracks recorded
   - Production plan finalized

**2. Recording Completion** (End of Month 2)
   - All tracks recorded
   - Initial mix completed
   - Feedback incorporated

**3. Final Mix** (Mid-Month 3)
   - Final mix approved
   - Mastering completed
   - Metadata prepared

**4. Release** (End of Month 4)
   - Distribution complete
   - Marketing materials ready
   - Release executed

`;
          break;
        case 'software':
        case 'app':
          milestonesText += `**1. MVP Definition** (End of Month 1)
   - Requirements documented
   - Architecture designed
   - Development environment set up

**2. Core Functionality** (End of Month 2)
   - Basic features implemented
   - Database integration complete
   - API endpoints functional

**3. Feature Complete** (Mid-Month 3)
   - All features implemented
   - Testing completed
   - User feedback incorporated

**4. Production Release** (End of Month 4)
   - Final QA passed
   - Documentation complete
   - Deployment executed

`;
          break;
        case 'film':
        case 'video':
          milestonesText += `**1. Pre-Production Complete** (End of Month 1)
   - Script finalized
   - Storyboards complete
   - Production schedule approved

**2. Production Complete** (End of Month 2)
   - Principal photography finished
   - All necessary footage captured
   - Sound recording completed

**3. Rough Cut** (Mid-Month 3)
   - Initial edit complete
   - Preliminary effects added
   - Feedback incorporated

**4. Final Cut** (End of Month 4)
   - Final edit approved
   - Color grading complete
   - Ready for distribution

`;
          break;
        default:
          milestonesText += `**1. Project Initiation** (End of Month 1)
   - Requirements gathered
   - Project plan approved
   - Resources allocated

**2. Development Milestone** (End of Month 2)
   - Core components developed
   - Initial integration complete
   - Progress review conducted

**3. Quality Assurance** (Mid-Month 3)
   - Testing completed
   - Refinements implemented
   - Validation performed

**4. Project Completion** (End of Month 4)
   - Final deliverables approved
   - Documentation completed
   - Project handover executed

`;
      }
    }

    return milestonesText;
  }

  /**
   * Generate Schedule A content
   * @param {Object} project - The project data
   * @returns {string} - The Schedule A content
   */
  _generateScheduleA(project) {
    const projectName = project.name || 'Project';
    const projectDescription = project.description || 'A collaborative project';
    const projectType = project.projectType || 'project';

    // Create a project type specific description
    let projectTypeDescription = '';
    switch (projectType) {
      case 'game':
        projectTypeDescription = 'game development';
        break;
      case 'music':
        projectTypeDescription = 'music production';
        break;
      case 'software':
      case 'app':
        projectTypeDescription = 'software development';
        break;
      case 'film':
      case 'video':
        projectTypeDescription = 'video production';
        break;
      default:
        projectTypeDescription = 'project development';
    }

    // Create a properly formatted Schedule A with all required sections
    return `This project involves ${projectTypeDescription} work on "${projectName}". ${projectDescription}

1. **Services**

   a. **General.** Pursuant to the terms and conditions of this Agreement and subject to Company's acceptance, Contributor shall:
      i. develop the Work Product following the requirements and technical specifications set forth in Exhibit I and in accordance with the roadmap set forth in Exhibit II and Good Industry Practice ("Developing Services"); and
      ii. provide the Support Services in accordance with Good Industry Practice.

   b. **Performance.** Contributor understands and agrees that Contributor is solely responsible for the control and supervision of the means by which the Services are provided consistent with the goal of successfully completing the Services on time. Contributor shall allocate sufficient resources to ensure that it performs its Services under this Agreement in accordance with the roadmap in Exhibit II and in accordance with Good Industry Practice.

   c. **Co-operation.**
      i. During the period from the Effective Date until Launch, unless directed otherwise by Company, Contributor shall attend at least weekly calls with Company, with frequency increasing during crunch periods as needed, and provide builds (application versions) every two (2) weeks with Company.
      ii. The Parties shall share responsibility for Work Product Management as agreed between the Parties from time to time and as outlined in Exhibit II. If the Parties are unable to reach agreement in respect of a decision in relation to Work Product Management, Company's decision shall prevail.
      iii. If there is additional need for development that is not in the agreed roadmap the Parties will negotiate in good faith the timeline for Contributor to deliver such further development. The Parties will also negotiate in good faith the costs to deliver such further development.

   d. **Cessation of Services following Launch & Right of First Refusal.**
      i. Following Launch, Company may determine that it no longer wishes to receive the Services and Contributor may determine that it no longer wishes to provide the Services, each at its sole discretion. In such case the relevant Party will notify the other Party in writing.
      ii. If Contributor notifies Company that it no longer wishes to provide Services to Company, Contributor's obligations to provide the Services will cease within 7 (seven) days of that notice (or such other period as agreed by the Parties) and this Agreement shall terminate automatically.
      iii. If Company intends to continue development of the Work Product after Contributor ceases providing Services, Company may notify Contributor in writing, and both Parties may negotiate in good faith the terms for such further development.

2. **Approval.**

   a. When Contributor considers that it has progressed the Work Product such that it reaches a Milestone, the Work Product shall be submitted to Company for written approval ("Approval"). Company will assess and/or test each delivered Milestone and will notify Contributor if it is accepted or rejected within 7 (seven) business days after receipt, though the parties acknowledge that in practice, feedback will typically be provided as soon as possible as determined by the development team. In case the Work Product does not, in Company's reasonable opinion, satisfy the Milestone or otherwise meet the technical specifications set forth in Exhibit I, Contributor will, at Company's request, promptly repair or redo the Services wholly or in part, with an initial response time of 7 (seven) days from the date on which feedback is received. For complex issues or features that reasonably require additional time, Contributor shall demonstrate meaningful progress during weekly meetings until the issue is resolved to Company's satisfaction.`;
  }

  /**
   * Replace Schedule A in the agreement
   * @param {string} template - The agreement template
   * @param {string} scheduleA - The Schedule A content
   * @returns {string} - The processed template
   */
  _replaceScheduleA(template, scheduleA) {
    let processed = template;

    // Format Schedule A content with proper markdown
    // Convert numbered sections to proper markdown headers
    let formattedScheduleA = scheduleA;
    formattedScheduleA = formattedScheduleA.replace(/^(\d+)\.\s+\*\*([A-Z][a-zA-Z\s]+)\*\*$/gm, '## $1. $2');

    // Convert lettered subsections to proper markdown subheaders
    formattedScheduleA = formattedScheduleA.replace(/^(\s*)([a-z])\.\s+\*\*([A-Z][a-zA-Z\s]+)\*\*$/gm, '$1### $2. $3');

    // Find and replace Schedule A
    const scheduleARegex = /## SCHEDULE A[\s\S]*?(?=## SCHEDULE B|$)/;
    if (scheduleARegex.test(processed)) {
      processed = processed.replace(scheduleARegex, `## SCHEDULE A\n### Description of Services\n\n${formattedScheduleA}\n\n`);
    } else {
      // If Schedule A is not found, add it before Schedule B
      const scheduleBIndex = processed.indexOf('## SCHEDULE B');
      if (scheduleBIndex !== -1) {
        processed = processed.substring(0, scheduleBIndex) +
          `## SCHEDULE A\n### Description of Services\n\n${formattedScheduleA}\n\n` +
          processed.substring(scheduleBIndex);
      } else {
        // If Schedule B is not found, add Schedule A at the end
        processed += `\n\n## SCHEDULE A\n### Description of Services\n\n${formattedScheduleA}\n\n`;
      }
    }

    return processed;
  }

  /**
   * Replace exhibits in the agreement
   * @param {string} template - The agreement template
   * @param {string} exhibitI - The Exhibit I content
   * @param {string} exhibitII - The Exhibit II content
   * @returns {string} - The processed template
   */
  _replaceExhibits(template, exhibitI, exhibitII) {
    console.log('EnhancedAgreementGenerator: Replacing exhibits in template');

    if (!template) {
      console.error('EnhancedAgreementGenerator: Template is empty or undefined in _replaceExhibits');
      return '';
    }

    if (!exhibitI) {
      console.warn('EnhancedAgreementGenerator: Exhibit I content is empty or undefined');
      exhibitI = 'Project specifications to be determined.';
    }

    if (!exhibitII) {
      console.warn('EnhancedAgreementGenerator: Exhibit II content is empty or undefined');
      exhibitII = 'Project roadmap to be determined.';
    }

    let processed = template;

    try {
      // EXTREME APPROACH: Find all sections and rebuild the document with our custom exhibits
      console.log('EnhancedAgreementGenerator: Using extreme approach to replace exhibits');

      // Split the document into major sections
      const sections = [];
      let currentPosition = 0;

      // Find all major section headers (##)
      const sectionHeaders = [...processed.matchAll(/^## [A-Z]+/gm)];

      if (sectionHeaders.length > 0) {
        console.log(`EnhancedAgreementGenerator: Found ${sectionHeaders.length} section headers`);

        // Process each section
        sectionHeaders.forEach((match, index) => {
          const startPos = match.index;
          const endPos = index < sectionHeaders.length - 1 ? sectionHeaders[index + 1].index : processed.length;
          const sectionContent = processed.substring(startPos, endPos);
          const sectionName = sectionContent.split('\n')[0].trim();

          console.log(`EnhancedAgreementGenerator: Processing section: ${sectionName}`);

          // Skip EXHIBIT I and EXHIBIT II as we'll replace them
          if (sectionName !== '## EXHIBIT I' && sectionName !== '## EXHIBIT II') {
            sections.push(sectionContent);
          }

          // If we're at EXHIBIT I, add our custom EXHIBIT I
          if (sectionName === '## EXHIBIT I') {
            console.log('EnhancedAgreementGenerator: Replacing EXHIBIT I with custom content');
            sections.push(`## EXHIBIT I\n### SPECIFICATIONS\n\n${exhibitI}\n\n`);
          }

          // If we're at EXHIBIT II, add our custom EXHIBIT II
          if (sectionName === '## EXHIBIT II') {
            console.log('EnhancedAgreementGenerator: Replacing EXHIBIT II with custom content');
            sections.push(`## EXHIBIT II\n### PRODUCT ROADMAP\n\n${exhibitII}\n\n`);
          }
        });

        // Rebuild the document
        processed = sections.join('');

        // Check if EXHIBIT I and EXHIBIT II were added
        if (!processed.includes('## EXHIBIT I')) {
          console.log('EnhancedAgreementGenerator: EXHIBIT I not found, adding it before SCHEDULE A');
          // Find SCHEDULE A
          const scheduleAIndex = processed.indexOf('## SCHEDULE A');
          if (scheduleAIndex !== -1) {
            // Add EXHIBIT I and EXHIBIT II before SCHEDULE A
            processed = processed.substring(0, scheduleAIndex) +
              `## EXHIBIT I\n### SPECIFICATIONS\n\n${exhibitI}\n\n` +
              `## EXHIBIT II\n### PRODUCT ROADMAP\n\n${exhibitII}\n\n` +
              processed.substring(scheduleAIndex);
          } else {
            // Add at the end
            processed += `\n\n## EXHIBIT I\n### SPECIFICATIONS\n\n${exhibitI}\n\n`;
            processed += `\n\n## EXHIBIT II\n### PRODUCT ROADMAP\n\n${exhibitII}\n\n`;
          }
        } else if (!processed.includes('## EXHIBIT II')) {
          console.log('EnhancedAgreementGenerator: EXHIBIT II not found, adding it after EXHIBIT I');
          // Find EXHIBIT I
          const exhibitIIndex = processed.indexOf('## EXHIBIT I');
          const nextSectionIndex = processed.indexOf('##', exhibitIIndex + 10);
          if (nextSectionIndex !== -1) {
            // Add EXHIBIT II after EXHIBIT I but before the next section
            processed = processed.substring(0, nextSectionIndex) +
              `## EXHIBIT II\n### PRODUCT ROADMAP\n\n${exhibitII}\n\n` +
              processed.substring(nextSectionIndex);
          } else {
            // Add at the end
            processed += `\n\n## EXHIBIT II\n### PRODUCT ROADMAP\n\n${exhibitII}\n\n`;
          }
        }
      } else {
        console.warn('EnhancedAgreementGenerator: No section headers found, using fallback approach');

        // FALLBACK: If we can't find section headers, use a more direct approach
        // Replace everything between "## EXHIBIT I" and "## SCHEDULE A" with our custom exhibits
        const exhibitIIndex = processed.indexOf('## EXHIBIT I');
        const scheduleAIndex = processed.indexOf('## SCHEDULE A');

        if (exhibitIIndex !== -1 && scheduleAIndex !== -1) {
          processed = processed.substring(0, exhibitIIndex) +
            `## EXHIBIT I\n### SPECIFICATIONS\n\n${exhibitI}\n\n` +
            `## EXHIBIT II\n### PRODUCT ROADMAP\n\n${exhibitII}\n\n` +
            processed.substring(scheduleAIndex);
        } else {
          console.error('EnhancedAgreementGenerator: Could not find EXHIBIT I or SCHEDULE A');
        }
      }

      // Verify that the exhibits were replaced
      if (!processed.includes(exhibitI.substring(0, 100))) {
        console.warn('EnhancedAgreementGenerator: Exhibit I content not found in processed template');
      }

      if (!processed.includes(exhibitII.substring(0, 100))) {
        console.warn('EnhancedAgreementGenerator: Exhibit II content not found in processed template');
      }

    } catch (error) {
      console.error('EnhancedAgreementGenerator: Error replacing exhibits:', error);
      // Return the original template if there's an error
      return template;
    }

    return processed;
  }
  /**
   * Final cleanup to catch any remaining placeholders
   * @param {string} template - The processed template
   * @param {Object} project - The project data
   * @param {Object} owner - The owner information
   * @param {Object} contributor - The contributor information
   * @returns {string} - The fully cleaned template
   */
  _finalCleanup(template, project, owner, contributor) {
    console.log('EnhancedAgreementGenerator: Performing final cleanup');

    if (!template) {
      console.error('EnhancedAgreementGenerator: Template is empty or undefined in _finalCleanup');
      return '';
    }

    let cleaned = template;
    const projectType = project.projectType || 'project';

    // Fix section headers and formatting
    console.log('EnhancedAgreementGenerator: Fixing section headers and formatting');

    // Fix main section headers (numbered sections)
    cleaned = cleaned.replace(/^(\d+)\.\s+([A-Z][a-zA-Z\s]+)\.?$/gm, '## $1. $2');

    // Fix subsection headers (lettered sections)
    cleaned = cleaned.replace(/^(\s*)([a-z])\.\s+([A-Z][a-zA-Z\s]+)\.?$/gm, '$1### $2. $3');

    // Fix Recitals section if not properly formatted
    if (!cleaned.includes('## Recitals') && cleaned.includes('Recitals')) {
      cleaned = cleaned.replace(/^Recitals$/m, '## Recitals');
    }

    // Fix Schedule and Exhibit headers
    cleaned = cleaned.replace(/^SCHEDULE ([A-Z])$/gm, '## SCHEDULE $1');
    cleaned = cleaned.replace(/^EXHIBIT ([A-Z]+)$/gm, '## EXHIBIT $1');

    // Fix Description headers in schedules and exhibits
    cleaned = cleaned.replace(/^#\s+Description of Services$/gm, '### Description of Services');
    cleaned = cleaned.replace(/^#\s+SPECIFICATIONS$/gm, '### SPECIFICATIONS');
    cleaned = cleaned.replace(/^#\s+PRODUCT ROADMAP$/gm, '### PRODUCT ROADMAP');

    // Add bold formatting to defined terms
    const definedTerms = [
      'Agreement', 'Background IP', 'Company', 'Confidential Information',
      'Contributor', 'Contributor IP', 'Developing Services', 'Effective Date',
      'Exhibit', 'Good Industry Practice', 'Launch', 'Milestone', 'Net Revenue',
      'Project', 'Revenue', 'Royalty', 'Schedule', 'Services', 'Support Services',
      'Term', 'Work Product', 'Work Product IP', 'Work Product Management'
    ];

    definedTerms.forEach(term => {
      // Only bold the term if it's a standalone word (with word boundaries)
      const regex = new RegExp(`\\b${term}\\b(?![^<]*>)`, 'g');
      cleaned = cleaned.replace(regex, `**${term}**`);
    });

    try {
      // AGGRESSIVE VOTA-SPECIFIC CONTENT REMOVAL
      console.log('EnhancedAgreementGenerator: Removing VOTA-specific content');

      // Replace specific VOTA content
      cleaned = cleaned.replace(/Village of The Ages/gi, project.name || 'Project');
      cleaned = cleaned.replace(/village simulation game/gi, projectType === 'game' ? 'game' : projectType);
      cleaned = cleaned.replace(/village simulation/gi, projectType === 'game' ? 'game' : projectType);

      // Replace specific VOTA phrases
      cleaned = cleaned.replace(/players guide communities through historical progressions/gi,
        projectType === 'game' ? 'players engage with interactive content' :
        projectType === 'music' ? 'listeners experience audio content' :
        projectType === 'software' || projectType === 'app' ? 'users interact with the application' :
        projectType === 'film' || projectType === 'video' ? 'viewers experience visual content' :
        'users engage with the project');

      cleaned = cleaned.replace(/manage resource-based challenges/gi,
        projectType === 'game' ? 'overcome various challenges' :
        projectType === 'music' ? 'experience different musical elements' :
        projectType === 'software' || projectType === 'app' ? 'utilize various features' :
        projectType === 'film' || projectType === 'video' ? 'experience the narrative' :
        'engage with project elements');

      // Replace village-specific content
      cleaned = cleaned.replace(/village building/gi,
        projectType === 'game' ? 'game development' :
        projectType === 'music' ? 'music composition' :
        projectType === 'software' || projectType === 'app' ? 'software development' :
        projectType === 'film' || projectType === 'video' ? 'film production' :
        'project development');

      cleaned = cleaned.replace(/village layout/gi,
        projectType === 'game' ? 'game design' :
        projectType === 'music' ? 'track arrangement' :
        projectType === 'software' || projectType === 'app' ? 'application architecture' :
        projectType === 'film' || projectType === 'video' ? 'scene composition' :
        'project structure');

      cleaned = cleaned.replace(/historical eras/gi,
        projectType === 'game' ? 'game levels' :
        projectType === 'music' ? 'musical movements' :
        projectType === 'software' || projectType === 'app' ? 'development phases' :
        projectType === 'film' || projectType === 'video' ? 'narrative arcs' :
        'project phases');

      cleaned = cleaned.replace(/resource gathering/gi,
        projectType === 'game' ? 'resource collection' :
        projectType === 'music' ? 'sound collection' :
        projectType === 'software' || projectType === 'app' ? 'data collection' :
        projectType === 'film' || projectType === 'video' ? 'asset collection' :
        'resource management');

      cleaned = cleaned.replace(/AI for villagers/gi,
        projectType === 'game' ? 'game AI' :
        projectType === 'music' ? 'audio processing' :
        projectType === 'software' || projectType === 'app' ? 'automated processes' :
        projectType === 'film' || projectType === 'video' ? 'visual effects' :
        'automated systems');

      cleaned = cleaned.replace(/trading systems with neighboring villages/gi,
        projectType === 'game' ? 'interaction systems' :
        projectType === 'music' ? 'collaborative elements' :
        projectType === 'software' || projectType === 'app' ? 'data exchange protocols' :
        projectType === 'film' || projectType === 'video' ? 'scene transitions' :
        'integration components');

      // Replace any remaining date placeholders
      cleaned = cleaned.replace(/\[ \], 20\[__\]/g, this.currentDate);
      cleaned = cleaned.replace(/\[_+\]/g, contributor.name || 'Contributor');
      cleaned = cleaned.replace(/\[__\]/g, '');
      cleaned = cleaned.replace(/\[\s*\]/g, '');

      // Replace any remaining project information
      cleaned = cleaned.replace(/\[Project Name\]/g, project.name || 'Project');

      // Replace any remaining company information only if available
      if (owner.company) {
        cleaned = cleaned.replace(/City of Gamers Inc\./gi, owner.company);
        cleaned = cleaned.replace(/City of Gamers/gi, owner.company);
        cleaned = cleaned.replace(/\bCOG\b/gi, owner.company);
      }

      if (owner.name) {
        cleaned = cleaned.replace(/Gynell Journigan/gi, owner.name);
      }

      // Replace any remaining location information only if available
      if (owner.state) {
        cleaned = cleaned.replace(/Florida/gi, owner.state);
        cleaned = cleaned.replace(/the applicable jurisdiction/gi, owner.state);
      }

      if (owner.city) {
        cleaned = cleaned.replace(/Orlando/gi, owner.city);
      }

      if (owner.address) {
        cleaned = cleaned.replace(/1205 43rd Street, Suite B, Orlando, Florida 32839/gi, owner.address);
      }

      // Handle county/jurisdiction only if city is available
      if (owner.city && owner.state) {
        // Try to determine the county name based on the city/state
        let countyName = '';
        if (owner.city.toLowerCase() === 'wilmington' && owner.state.toLowerCase() === 'delaware') {
          countyName = 'NEW CASTLE COUNTY';
        } else {
          // If we don't have a specific mapping, use the city name with COUNTY
          countyName = owner.city.toUpperCase() + ' COUNTY';
        }
        cleaned = cleaned.replace(/ORANGE COUNTY/gi, countyName);
        cleaned = cleaned.replace(/Orange County/gi, countyName.charAt(0) + countyName.slice(1).toLowerCase());
      }

      // Replace any remaining contact information only if available
      if (owner.email) {
        // Make sure to replace all instances of personal email addresses with company email
        const emailReplacements = [
          /billing@cogfuture\.com/gi,
          /thesolmarauder@gmail\.com/gi,
          /solmarauder@gmail\.com/gi,
          /thesolmarauder@/gi,
          /\[Project Owner Email\]/gi,
          /\[Company Email\]/gi,
          /\[Email\]/gi
        ];

        emailReplacements.forEach(pattern => {
          cleaned = cleaned.replace(pattern, owner.email);
        });
      }

      // Replace any remaining contributor information only if available
      if (contributor.name) {
        cleaned = cleaned.replace(/\[Contributor\]/g, contributor.name);
        cleaned = cleaned.replace(/\[Contributor Name\]/g, contributor.name);
        cleaned = cleaned.replace(/\[CONTRIBUTOR NAME\]/g, contributor.name.toUpperCase());
      }

      // Replace any remaining game-specific terms
      if (projectType !== 'game') {
        console.log('EnhancedAgreementGenerator: Replacing game-specific terms for project type:', projectType);

        // Replace "game" with appropriate project type
        cleaned = cleaned.replace(/\bgame\b/gi, projectType);
        cleaned = cleaned.replace(/\bgames\b/gi, projectType + 's');

        // Replace "gameplay" with appropriate experience term
        cleaned = cleaned.replace(/\bgameplay\b/gi,
          projectType === 'music' ? 'listening experience' :
          projectType === 'software' || projectType === 'app' ? 'user experience' :
          projectType === 'film' || projectType === 'video' ? 'viewing experience' :
          'user experience');

        // Replace "player" with appropriate user term
        cleaned = cleaned.replace(/\bplayer\b/gi,
          projectType === 'music' ? 'listener' :
          projectType === 'software' || projectType === 'app' ? 'user' :
          projectType === 'film' || projectType === 'video' ? 'viewer' :
          'user');

        // Replace "players" with appropriate users term
        cleaned = cleaned.replace(/\bplayers\b/gi,
          projectType === 'music' ? 'listeners' :
          projectType === 'software' || projectType === 'app' ? 'users' :
          projectType === 'film' || projectType === 'video' ? 'viewers' :
          'users');

        // Replace platform-specific terms
        cleaned = cleaned.replace(/Steam, Epic Games Store, console platform/gi,
          projectType === 'music' ? 'Spotify, Apple Music, streaming platforms' :
          projectType === 'software' || projectType === 'app' ? 'App Store, Google Play, distribution platforms' :
          projectType === 'film' || projectType === 'video' ? 'Netflix, Amazon Prime, streaming platforms' :
          'distribution platforms');

        // Replace "game ready" with appropriate term
        cleaned = cleaned.replace(/game ready/gi,
          projectType === 'music' ? 'release ready' :
          projectType === 'software' || projectType === 'app' ? 'deployment ready' :
          projectType === 'film' || projectType === 'video' ? 'screening ready' :
          'release ready');

        // Replace "early access" with appropriate term
        cleaned = cleaned.replace(/early access/gi,
          projectType === 'music' ? 'preview release' :
          projectType === 'software' || projectType === 'app' ? 'beta release' :
          projectType === 'film' || projectType === 'video' ? 'preview screening' :
          'initial release');
      }

      // Replace any remaining placeholders with empty strings
      cleaned = cleaned.replace(/\[.*?\]/g, '');

      // Fix signature block
      cleaned = cleaned.replace(/\[If a company\]/g, '');
      cleaned = cleaned.replace(/\[Name of Company\]/g, contributor.isCompany ? contributor.name : '');
      cleaned = cleaned.replace(/\[If an individual\]/g, '');

      // Fix double articles
      cleaned = cleaned.replace(/a\s+a\s+/gi, 'a ');
      cleaned = cleaned.replace(/an\s+a\s+/gi, 'an ');
      cleaned = cleaned.replace(/a\s+an\s+/gi, 'an ');

      // Fix spacing issues
      cleaned = cleaned.replace(/\s+\./g, '.');
      cleaned = cleaned.replace(/\s+,/g, ',');
      cleaned = cleaned.replace(/\s+;/g, ';');
      cleaned = cleaned.replace(/\s+:/g, ':');
      cleaned = cleaned.replace(/\s+\)/g, ')');
      cleaned = cleaned.replace(/\(\s+/g, '(');

      // Fix multiple consecutive newlines (more than 2)
      cleaned = cleaned.replace(/\n{3,}/g, '\n\n');

      // Check for any remaining VOTA-specific content
      const votaTerms = ['Village of The Ages', 'village simulation', 'historical progressions', 'resource-based challenges'];
      votaTerms.forEach(term => {
        if (cleaned.includes(term)) {
          console.warn(`EnhancedAgreementGenerator: Agreement still contains VOTA-specific term: "${term}"`);
        }
      });

      // Check for any remaining game-specific content if not a game project
      if (projectType !== 'game') {
        const gameTerms = ['game', 'gameplay', 'player', 'players', 'Steam', 'Epic Games Store'];
        gameTerms.forEach(term => {
          if (cleaned.toLowerCase().includes(term.toLowerCase())) {
            console.warn(`EnhancedAgreementGenerator: Agreement still contains game-specific term: "${term}"`);
          }
        });
      }

    } catch (error) {
      console.error('EnhancedAgreementGenerator: Error in final cleanup:', error);
      // Return the original template if there's an error
      return template;
    }

    return cleaned;
  }

  /**
   * Replace royalty model information in the agreement
   * @param {string} template - The agreement template
   * @param {Object} royaltyModel - The royalty model
   * @param {Object} project - The project data
   * @returns {string} - The processed template
   */
  _replaceRoyaltyModelInfo(template, royaltyModel, project) {
    if (!royaltyModel) return template;

    let processed = template;

    // Replace royalty percentage
    if (royaltyModel.contributor_percentage) {
      processed = processed.replace(/33%/g, `${royaltyModel.contributor_percentage}%`);
    }

    // Replace minimum payout threshold
    if (royaltyModel.min_payout) {
      processed = processed.replace(/\$100,000/g, `$${(royaltyModel.min_payout / 100).toFixed(2)}`);
    }

    // Replace maximum payout
    if (royaltyModel.max_payout) {
      processed = processed.replace(/\$1,000,000/g, `$${(royaltyModel.max_payout / 100).toFixed(2)}`);
    }

    // Replace weights in calculation example
    if (royaltyModel.configuration) {
      const { tasks_weight, hours_weight, difficulty_weight } = royaltyModel.configuration;

      if (tasks_weight) {
        processed = processed.replace(/Tasks Completed: 30%/g, `Tasks Completed: ${tasks_weight}%`);
        processed = processed.replace(/40 × 0\.3 = 12/g, `40 × ${tasks_weight/100} = ${Math.round(40 * tasks_weight/100)}`);
      }

      if (hours_weight) {
        processed = processed.replace(/Hours Worked: 30%/g, `Hours Worked: ${hours_weight}%`);
        processed = processed.replace(/20 × 0\.3 = 6/g, `20 × ${hours_weight/100} = ${Math.round(20 * hours_weight/100)}`);
      }

      if (difficulty_weight) {
        processed = processed.replace(/Task Difficulty: 40%/g, `Task Difficulty: ${difficulty_weight}%`);
        processed = processed.replace(/40 × 0\.4 = 16/g, `40 × ${difficulty_weight/100} = ${Math.round(40 * difficulty_weight/100)}`);
      }
    }

    // Update the calculation example
    processed = processed.replace(/Total Contribution Score = 12 \+ 6 \+ 16 = 34/g, () => {
      const tasksScore = Math.round(40 * (royaltyModel.configuration?.tasks_weight || 33) / 100);
      const hoursScore = Math.round(20 * (royaltyModel.configuration?.hours_weight || 33) / 100);
      const difficultyScore = Math.round(40 * (royaltyModel.configuration?.difficulty_weight || 34) / 100);
      const totalScore = tasksScore + hoursScore + difficultyScore;
      return `Total Contribution Score = ${tasksScore} + ${hoursScore} + ${difficultyScore} = ${totalScore}`;
    });

    processed = processed.replace(/\(34 ÷ 110\) × 100 = 30\.91%/g, () => {
      const tasksScore = Math.round(40 * (royaltyModel.configuration?.tasks_weight || 33) / 100);
      const hoursScore = Math.round(20 * (royaltyModel.configuration?.hours_weight || 33) / 100);
      const difficultyScore = Math.round(40 * (royaltyModel.configuration?.difficulty_weight || 34) / 100);
      const totalScore = tasksScore + hoursScore + difficultyScore;
      const percentage = ((totalScore / 110) * 100).toFixed(2);
      return `(${totalScore} ÷ 110) × 100 = ${percentage}%`;
    });

    processed = processed.replace(/30\.91% of \$10,000 = \$3,091/g, () => {
      const tasksScore = Math.round(40 * (royaltyModel.configuration?.tasks_weight || 33) / 100);
      const hoursScore = Math.round(20 * (royaltyModel.configuration?.hours_weight || 33) / 100);
      const difficultyScore = Math.round(40 * (royaltyModel.configuration?.difficulty_weight || 34) / 100);
      const totalScore = tasksScore + hoursScore + difficultyScore;
      const percentage = ((totalScore / 110) * 100).toFixed(2);
      const amount = ((totalScore / 110) * 10000).toFixed(0);
      return `${percentage}% of $10,000 = $${amount}`;
    });

    return processed;
  }
}

// Create and export a singleton instance
export const enhancedAgreementGenerator = new EnhancedAgreementGenerator();
