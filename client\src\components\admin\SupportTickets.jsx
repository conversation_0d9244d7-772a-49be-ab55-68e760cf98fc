import React, { useState, useEffect } from 'react';
import { Card, CardBody, CardHeader, Button, Chip, Input, Select, SelectItem, Modal, ModalContent, ModalHeader, ModalBody, ModalFooter, Textarea, Table, TableHeader, TableColumn, TableBody, TableRow, TableCell } from '@heroui/react';
import { motion } from 'framer-motion';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';

/**
 * Support Tickets Component - Help Desk and Ticket Management System
 * 
 * Features:
 * - Support ticket queue and management
 * - Ticket assignment and tracking
 * - User communication and resolution
 * - Priority and category management
 * - Response time tracking and SLA monitoring
 */
const SupportTickets = ({ currentUser, className = "" }) => {
  const [loading, setLoading] = useState(true);
  const [tickets, setTickets] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('open');
  const [priorityFilter, setPriorityFilter] = useState('all');
  const [selectedTicket, setSelectedTicket] = useState(null);
  const [showTicketModal, setShowTicketModal] = useState(false);
  const [responseText, setResponseText] = useState('');
  const [ticketAction, setTicketAction] = useState('');

  // Load support tickets
  const loadTickets = async () => {
    try {
      setLoading(true);
      
      const { data: { session } } = await supabase.auth.getSession();
      const authToken = session?.access_token;
      
      if (!authToken) {
        toast.error('Authentication required');
        return;
      }

      // Fetch real support tickets data from Supabase
      const { data: ticketsData, error: ticketsError } = await supabase
        .from('support_tickets')
        .select(`
          *,
          user:users!support_tickets_user_id_fkey(
            id,
            display_name,
            email
          ),
          responses:support_ticket_responses(
            id,
            message,
            created_at,
            is_admin,
            author:users!support_ticket_responses_author_id_fkey(
              display_name
            )
          )
        `)
        .order('created_at', { ascending: false })
        .limit(100);

      if (ticketsError) throw ticketsError;

      // Transform data to match component expectations
      const transformedTickets = ticketsData?.map(ticket => ({
        id: ticket.id,
        ticketNumber: ticket.ticket_number || `#${ticket.id}`,
        subject: ticket.subject,
        description: ticket.description,
        category: ticket.category,
        priority: ticket.priority,
        status: ticket.status,
        user: {
          name: ticket.user?.display_name || 'Unknown User',
          email: ticket.user?.email || '<EMAIL>',
          id: ticket.user?.id || 'unknown'
        },
        assignedTo: ticket.assigned_to,
        createdAt: ticket.created_at,
        updatedAt: ticket.updated_at,
        resolvedAt: ticket.resolved_at,
        responses: ticket.responses?.map(response => ({
          id: response.id,
          author: response.author?.display_name || 'System',
          message: response.message,
          timestamp: response.created_at,
          isAdmin: response.is_admin
        })) || [],
        tags: ticket.tags || []
      })) || [];

      setTickets(transformedTickets);
      
    } catch (error) {
      console.error('Error loading tickets:', error);
      toast.error('Failed to load support tickets');
    } finally {
      setLoading(false);
    }
  };

  // Filter tickets
  const filteredTickets = tickets.filter(ticket => {
    const matchesSearch = ticket.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         ticket.ticketNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         ticket.user.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || ticket.status === statusFilter;
    const matchesPriority = priorityFilter === 'all' || ticket.priority === priorityFilter;
    
    return matchesSearch && matchesStatus && matchesPriority;
  });

  // Get priority color
  const getPriorityColor = (priority) => {
    const colors = {
      'urgent': 'danger',
      'high': 'warning',
      'normal': 'primary',
      'low': 'default'
    };
    return colors[priority] || 'default';
  };

  // Get status color
  const getStatusColor = (status) => {
    const colors = {
      'open': 'warning',
      'in_progress': 'primary',
      'resolved': 'success',
      'closed': 'default'
    };
    return colors[status] || 'default';
  };

  // Get category display
  const getCategoryDisplay = (category) => {
    const categories = {
      'technical': 'Technical',
      'billing': 'Billing',
      'account': 'Account',
      'feature_request': 'Feature Request',
      'general': 'General'
    };
    return categories[category] || category;
  };

  // Handle ticket action
  const handleTicketAction = (ticket, action) => {
    setSelectedTicket(ticket);
    setTicketAction(action);
    setResponseText('');
    setShowTicketModal(true);
  };

  // Execute ticket action
  const executeTicketAction = async () => {
    try {
      if (ticketAction === 'respond' && !responseText.trim()) {
        toast.error('Please enter a response message');
        return;
      }

      // Mock API call - in production this would call the support API
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Update ticket locally
      setTickets(prevTickets => 
        prevTickets.map(ticket => {
          if (ticket.id === selectedTicket.id) {
            const updatedTicket = { ...ticket };
            
            if (ticketAction === 'respond') {
              updatedTicket.responses = [
                ...ticket.responses,
                {
                  id: `resp-${Date.now()}`,
                  author: currentUser?.display_name || 'Admin',
                  message: responseText,
                  timestamp: new Date().toISOString(),
                  isAdmin: true
                }
              ];
              updatedTicket.status = 'in_progress';
              updatedTicket.assignedTo = currentUser?.display_name || 'Admin';
            } else if (ticketAction === 'resolve') {
              updatedTicket.status = 'resolved';
              updatedTicket.resolvedAt = new Date().toISOString();
              if (responseText.trim()) {
                updatedTicket.responses = [
                  ...ticket.responses,
                  {
                    id: `resp-${Date.now()}`,
                    author: currentUser?.display_name || 'Admin',
                    message: responseText,
                    timestamp: new Date().toISOString(),
                    isAdmin: true
                  }
                ];
              }
            } else if (ticketAction === 'assign') {
              updatedTicket.assignedTo = currentUser?.display_name || 'Admin';
              updatedTicket.status = 'in_progress';
            }
            
            updatedTicket.updatedAt = new Date().toISOString();
            return updatedTicket;
          }
          return ticket;
        })
      );

      toast.success(`Ticket ${ticketAction}d successfully`);
      setShowTicketModal(false);
      setSelectedTicket(null);
      setTicketAction('');
      setResponseText('');
      
    } catch (error) {
      console.error('Error executing ticket action:', error);
      toast.error('Failed to execute action');
    }
  };

  // Format timestamp
  const formatTimestamp = (timestamp) => {
    return new Date(timestamp).toLocaleString();
  };

  // Calculate response time
  const calculateResponseTime = (createdAt, updatedAt) => {
    const created = new Date(createdAt);
    const updated = new Date(updatedAt);
    const diffHours = Math.round((updated - created) / (1000 * 60 * 60));
    return diffHours > 0 ? `${diffHours}h` : '<1h';
  };

  useEffect(() => {
    loadTickets();
  }, []);

  if (loading) {
    return (
      <div className={`flex items-center justify-center h-64 ${className}`}>
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <Card className="bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-3">
              <span className="text-3xl">🎫</span>
              <div>
                <h2 className="text-2xl font-bold">Support Tickets</h2>
                <p className="text-default-600">Manage user support requests and help desk</p>
              </div>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold text-warning">
                {filteredTickets.filter(ticket => ticket.status === 'open').length}
              </div>
              <div className="text-sm text-default-600">Open Tickets</div>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Search and Filters */}
      <Card>
        <CardBody className="p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <Input
              placeholder="Search tickets by subject, number, or user..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              startContent={<span>🔍</span>}
              className="flex-1"
            />
            
            <Select
              label="Status"
              selectedKeys={[statusFilter]}
              onSelectionChange={(keys) => setStatusFilter(Array.from(keys)[0])}
              className="w-40"
            >
              <SelectItem key="all">All Status</SelectItem>
              <SelectItem key="open">Open</SelectItem>
              <SelectItem key="in_progress">In Progress</SelectItem>
              <SelectItem key="resolved">Resolved</SelectItem>
              <SelectItem key="closed">Closed</SelectItem>
            </Select>
            
            <Select
              label="Priority"
              selectedKeys={[priorityFilter]}
              onSelectionChange={(keys) => setPriorityFilter(Array.from(keys)[0])}
              className="w-40"
            >
              <SelectItem key="all">All Priority</SelectItem>
              <SelectItem key="urgent">Urgent</SelectItem>
              <SelectItem key="high">High</SelectItem>
              <SelectItem key="normal">Normal</SelectItem>
              <SelectItem key="low">Low</SelectItem>
            </Select>
          </div>
        </CardBody>
      </Card>

      {/* Tickets Table */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">Support Tickets ({filteredTickets.length})</h3>
        </CardHeader>
        <CardBody>
          <Table aria-label="Support tickets table">
            <TableHeader>
              <TableColumn>TICKET</TableColumn>
              <TableColumn>USER</TableColumn>
              <TableColumn>PRIORITY</TableColumn>
              <TableColumn>STATUS</TableColumn>
              <TableColumn>ASSIGNED</TableColumn>
              <TableColumn>ACTIONS</TableColumn>
            </TableHeader>
            <TableBody>
              {filteredTickets.map((ticket) => (
                <TableRow key={ticket.id}>
                  <TableCell>
                    <div>
                      <div className="font-semibold">{ticket.ticketNumber}</div>
                      <div className="text-sm text-default-600">{ticket.subject}</div>
                      <div className="text-xs text-default-500">
                        {getCategoryDisplay(ticket.category)} • {formatTimestamp(ticket.createdAt)}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <div className="font-medium">{ticket.user.name}</div>
                      <div className="text-sm text-default-600">{ticket.user.email}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Chip
                      color={getPriorityColor(ticket.priority)}
                      size="sm"
                      variant="flat"
                    >
                      {ticket.priority}
                    </Chip>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <Chip
                        color={getStatusColor(ticket.status)}
                        size="sm"
                        variant="flat"
                      >
                        {ticket.status.replace('_', ' ')}
                      </Chip>
                      <div className="text-xs text-default-500">
                        Response: {calculateResponseTime(ticket.createdAt, ticket.updatedAt)}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      {ticket.assignedTo || 'Unassigned'}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Button
                        size="sm"
                        variant="flat"
                        onClick={() => handleTicketAction(ticket, 'view')}
                      >
                        View
                      </Button>
                      
                      {ticket.status !== 'resolved' && (
                        <>
                          <Button
                            size="sm"
                            variant="flat"
                            color="primary"
                            onClick={() => handleTicketAction(ticket, 'respond')}
                          >
                            Respond
                          </Button>
                          
                          {!ticket.assignedTo && (
                            <Button
                              size="sm"
                              variant="flat"
                              color="secondary"
                              onClick={() => handleTicketAction(ticket, 'assign')}
                            >
                              Assign
                            </Button>
                          )}
                          
                          <Button
                            size="sm"
                            variant="flat"
                            color="success"
                            onClick={() => handleTicketAction(ticket, 'resolve')}
                          >
                            Resolve
                          </Button>
                        </>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardBody>
      </Card>

      {/* Ticket Details/Action Modal */}
      <Modal 
        isOpen={showTicketModal} 
        onClose={() => setShowTicketModal(false)}
        size="3xl"
        scrollBehavior="inside"
      >
        <ModalContent>
          <ModalHeader>
            <h3>
              {ticketAction === 'view' ? 'Ticket Details' : 
               ticketAction === 'respond' ? 'Respond to Ticket' :
               ticketAction === 'resolve' ? 'Resolve Ticket' :
               'Assign Ticket'}: {selectedTicket?.ticketNumber}
            </h3>
          </ModalHeader>
          <ModalBody>
            {selectedTicket && (
              <div className="space-y-4">
                {/* Ticket Info */}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium">Subject</label>
                    <div className="text-sm text-default-600">{selectedTicket.subject}</div>
                  </div>
                  <div>
                    <label className="text-sm font-medium">User</label>
                    <div className="text-sm text-default-600">
                      {selectedTicket.user.name} ({selectedTicket.user.email})
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Priority</label>
                    <div>
                      <Chip color={getPriorityColor(selectedTicket.priority)} size="sm">
                        {selectedTicket.priority}
                      </Chip>
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Status</label>
                    <div>
                      <Chip color={getStatusColor(selectedTicket.status)} size="sm">
                        {selectedTicket.status.replace('_', ' ')}
                      </Chip>
                    </div>
                  </div>
                </div>

                {/* Description */}
                <div>
                  <label className="text-sm font-medium">Description</label>
                  <div className="mt-1 p-3 bg-default-100 rounded-lg text-sm">
                    {selectedTicket.description}
                  </div>
                </div>

                {/* Conversation */}
                {selectedTicket.responses.length > 0 && (
                  <div>
                    <label className="text-sm font-medium">Conversation</label>
                    <div className="mt-2 space-y-3 max-h-64 overflow-y-auto">
                      {selectedTicket.responses.map((response) => (
                        <div
                          key={response.id}
                          className={`p-3 rounded-lg ${
                            response.isAdmin 
                              ? 'bg-primary-50 border-l-4 border-primary' 
                              : 'bg-default-100'
                          }`}
                        >
                          <div className="flex items-center justify-between mb-1">
                            <span className="text-sm font-medium">{response.author}</span>
                            <span className="text-xs text-default-500">
                              {formatTimestamp(response.timestamp)}
                            </span>
                          </div>
                          <div className="text-sm">{response.message}</div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Response Input */}
                {(ticketAction === 'respond' || ticketAction === 'resolve') && (
                  <Textarea
                    label={ticketAction === 'resolve' ? 'Resolution Notes (optional)' : 'Response Message'}
                    placeholder={
                      ticketAction === 'resolve' 
                        ? 'Provide resolution details...'
                        : 'Type your response to the user...'
                    }
                    value={responseText}
                    onChange={(e) => setResponseText(e.target.value)}
                    minRows={4}
                  />
                )}

                {ticketAction === 'assign' && (
                  <div className="p-3 bg-info-50 rounded-lg">
                    <p className="text-sm">
                      This ticket will be assigned to you ({currentUser?.display_name || 'Admin'}) 
                      and status will be updated to "In Progress".
                    </p>
                  </div>
                )}
              </div>
            )}
          </ModalBody>
          <ModalFooter>
            <Button color="danger" variant="flat" onPress={() => setShowTicketModal(false)}>
              {ticketAction === 'view' ? 'Close' : 'Cancel'}
            </Button>
            {ticketAction !== 'view' && (
              <Button 
                color="primary" 
                onPress={executeTicketAction}
                disabled={ticketAction === 'respond' && !responseText.trim()}
              >
                {ticketAction === 'respond' ? 'Send Response' :
                 ticketAction === 'resolve' ? 'Resolve Ticket' :
                 'Assign to Me'}
              </Button>
            )}
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
};

export default SupportTickets;
