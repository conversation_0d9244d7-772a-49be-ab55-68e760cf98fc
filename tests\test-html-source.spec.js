import { test, expect } from '@playwright/test';

test.describe('HTML Source Analysis', () => {
  test('Check production HTML source', async ({ page }) => {
    console.log('🚀 Analyzing production HTML source...');
    
    // Navigate to the site
    await page.goto('https://royalty.technology');
    
    // Get the full HTML source
    const htmlSource = await page.content();
    
    // Look for script tags
    const scriptMatches = htmlSource.match(/<script[^>]*src[^>]*>/g) || [];
    
    console.log('\n📜 Script Tags Found:');
    scriptMatches.forEach((script, index) => {
      console.log(`  ${index + 1}. ${script}`);
    });
    
    // Check if main.jsx is referenced
    const hasMainJsx = htmlSource.includes('/src/main.jsx');
    const hasMainBundle = htmlSource.includes('main-');
    
    console.log('\n🔍 Script Analysis:');
    console.log('📄 References /src/main.jsx:', hasMainJsx ? '✅ YES' : '❌ NO');
    console.log('📦 References main bundle:', hasMainBundle ? '✅ YES' : '❌ NO');
    
    // Look for the root div
    const hasRootDiv = htmlSource.includes('<div id="root">');
    console.log('🏠 Has root div:', hasRootDiv ? '✅ YES' : '❌ NO');
    
    // Check if the bundle script is actually present
    if (hasMainBundle) {
      const bundleMatch = htmlSource.match(/main-[^"']+\.js/);
      if (bundleMatch) {
        console.log('📦 Bundle filename:', bundleMatch[0]);
      }
    }
    
    // Print relevant parts of HTML
    console.log('\n📋 HTML Body Section:');
    const bodyMatch = htmlSource.match(/<body[^>]*>(.*?)<\/body>/s);
    if (bodyMatch) {
      console.log(bodyMatch[1]);
    }
    
    expect(true).toBeTruthy();
  });
});
