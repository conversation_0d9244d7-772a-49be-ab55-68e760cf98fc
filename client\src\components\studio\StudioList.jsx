// StudioList - Enhanced studio listing with discovery and management
// Replaces basic team listing with comprehensive studio management
import React, { useState, useEffect, useContext } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Card, CardBody, Button, Badge, Input, Select, SelectItem } from '@heroui/react';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { 
  Crown, 
  Users, 
  Plus, 
  Search, 
  Filter,
  Building,
  TrendingUp,
  Calendar,
  MapPin,
  Star,
  Shield,
  User
} from 'lucide-react';

const StudioList = () => {
  const { currentUser } = useContext(UserContext);
  const navigate = useNavigate();
  
  // State management
  const [studios, setStudios] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState('my-studios');
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [filterRole, setFilterRole] = useState('all');

  // Fetch studios
  useEffect(() => {
    if (currentUser) {
      fetchStudios();
    }
  }, [currentUser, activeTab]);

  const fetchStudios = async () => {
    try {
      setLoading(true);
      setError(null);

      let query;
      
      if (activeTab === 'my-studios') {
        // Get studios where user is a member
        query = supabase
          .from('teams')
          .select(`
            *,
            team_members!inner(
              id,
              user_id,
              role,
              status,
              joined_at,
              collaboration_type,
              engagement_duration
            )
          `)
          .eq('team_members.user_id', currentUser.id)
          .eq('team_members.status', 'active');
      } else {
        // Get public studios for discovery
        query = supabase
          .from('teams')
          .select(`
            *,
            team_members(
              id,
              user_id,
              role,
              status,
              collaboration_type,
              engagement_duration,
              users(
                id,
                display_name,
                avatar_url
              )
            )
          `)
          .not('studio_type', 'is', null); // Only get studios with studio_type set
      }

      const { data, error: fetchError } = await query
        .order('created_at', { ascending: false });

      if (fetchError) throw fetchError;

      // Process studios data
      const processedStudios = data.map(studio => {
        const userMembership = studio.team_members?.find(m => m.user_id === currentUser.id);
        
        return {
          ...studio,
          userRole: userMembership?.role || null,
          collaborationType: userMembership?.collaboration_type || null,
          engagementDuration: userMembership?.engagement_duration || null,
          memberCount: studio.team_members?.filter(m => m.status === 'active').length || 0,
          isUserMember: !!userMembership
        };
      });

      setStudios(processedStudios);
    } catch (err) {
      console.error('Error fetching studios:', err);
      setError('Failed to load studios');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateStudio = () => {
    navigate('/studios/create');
  };

  const handleStudioClick = (studioId) => {
    navigate(`/studios/${studioId}`);
  };

  const handleJoinStudio = async (studioId) => {
    try {
      // Implementation for joining a studio
      console.log('Joining studio:', studioId);
      // This would typically send a join request or invitation
    } catch (err) {
      console.error('Error joining studio:', err);
    }
  };

  const getStudioTypeIcon = (studioType) => {
    switch (studioType) {
      case 'emerging': return '🌱';
      case 'established': return '🏰';
      case 'solo': return '⚔️';
      default: return '🏢';
    }
  };

  const getStudioTypeBadgeColor = (studioType) => {
    switch (studioType) {
      case 'emerging': return 'success';
      case 'established': return 'primary';
      case 'solo': return 'warning';
      default: return 'default';
    }
  };

  const getRoleBadgeColor = (role) => {
    switch (role) {
      case 'founder': return 'warning';
      case 'owner': return 'warning';
      case 'admin': return 'primary';
      default: return 'default';
    }
  };

  const filteredStudios = studios.filter(studio => {
    const matchesSearch = !searchQuery || 
      studio.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      studio.description?.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesType = filterType === 'all' || studio.studio_type === filterType;
    const matchesRole = filterRole === 'all' || studio.userRole === filterRole;
    
    return matchesSearch && matchesType && matchesRole;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading studios...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="text-red-500 mb-4">⚠️</div>
          <p className="text-red-600">{error}</p>
          <Button 
            onClick={fetchStudios}
            className="mt-4"
            variant="outline"
          >
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto p-6">
      {/* Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Studios</h1>
          <p className="text-gray-600">
            {activeTab === 'my-studios' 
              ? 'Manage your creative studios and collaborations'
              : 'Discover and join creative studios'
            }
          </p>
        </div>
        
        <div className="flex gap-3 mt-4 md:mt-0">
          <Button
            onClick={handleCreateStudio}
            color="primary"
            startContent={<Plus size={18} />}
            className="bg-purple-600 hover:bg-purple-700"
          >
            Create Studio
          </Button>
        </div>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 mb-6">
        <Button
          variant={activeTab === 'my-studios' ? 'solid' : 'light'}
          color={activeTab === 'my-studios' ? 'primary' : 'default'}
          onClick={() => setActiveTab('my-studios')}
          className="rounded-lg"
        >
          My Studios
        </Button>
        <Button
          variant={activeTab === 'discover' ? 'solid' : 'light'}
          color={activeTab === 'discover' ? 'primary' : 'default'}
          onClick={() => setActiveTab('discover')}
          className="rounded-lg"
        >
          Discover
        </Button>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardBody className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <Input
              placeholder="Search studios..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              startContent={<Search size={18} />}
              className="flex-1"
            />
            <Select
              placeholder="Filter by type"
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
              className="md:w-48"
            >
              <SelectItem key="all" value="all">All Types</SelectItem>
              <SelectItem key="emerging" value="emerging">🌱 Emerging</SelectItem>
              <SelectItem key="established" value="established">🏰 Established</SelectItem>
              <SelectItem key="solo" value="solo">⚔️ Solo</SelectItem>
            </Select>
            {activeTab === 'my-studios' && (
              <Select
                placeholder="Filter by role"
                value={filterRole}
                onChange={(e) => setFilterRole(e.target.value)}
                className="md:w-48"
              >
                <SelectItem key="all" value="all">All Roles</SelectItem>
                <SelectItem key="founder" value="founder">Founder</SelectItem>
                <SelectItem key="owner" value="owner">Owner</SelectItem>
                <SelectItem key="admin" value="admin">Admin</SelectItem>
                <SelectItem key="member" value="member">Member</SelectItem>
              </Select>
            )}
          </div>
        </CardBody>
      </Card>

      {/* Studios Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-6">
        {filteredStudios.map((studio, index) => (
          <motion.div
            key={studio.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <Card 
              className="h-full hover:shadow-lg transition-shadow cursor-pointer"
              isPressable
              onPress={() => handleStudioClick(studio.id)}
            >
              <CardBody className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="text-2xl">
                      {getStudioTypeIcon(studio.studio_type)}
                    </div>
                    <div>
                      <h3 className="font-semibold text-lg">{studio.name}</h3>
                      <Badge 
                        color={getStudioTypeBadgeColor(studio.studio_type)}
                        variant="flat"
                        size="sm"
                      >
                        {studio.studio_type}
                      </Badge>
                    </div>
                  </div>
                  
                  {studio.userRole && (
                    <Badge 
                      color={getRoleBadgeColor(studio.userRole)}
                      variant="flat"
                      size="sm"
                    >
                      {studio.userRole}
                    </Badge>
                  )}
                </div>

                <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                  {studio.description || 'No description available'}
                </p>

                <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                  <div className="flex items-center space-x-1">
                    <Users size={14} />
                    <span>{studio.memberCount} members</span>
                  </div>
                  
                  {studio.industry && (
                    <div className="flex items-center space-x-1">
                      <Building size={14} />
                      <span>{studio.industry}</span>
                    </div>
                  )}
                </div>

                {activeTab === 'discover' && !studio.isUserMember && (
                  <Button
                    size="sm"
                    color="primary"
                    variant="flat"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleJoinStudio(studio.id);
                    }}
                    className="w-full"
                  >
                    Request to Join
                  </Button>
                )}
              </CardBody>
            </Card>
          </motion.div>
        ))}
      </div>

      {filteredStudios.length === 0 && (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            {activeTab === 'my-studios' ? '🏢' : '🔍'}
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {activeTab === 'my-studios' ? 'No studios yet' : 'No studios found'}
          </h3>
          <p className="text-gray-600 mb-6">
            {activeTab === 'my-studios' 
              ? 'Create your first studio to start collaborating'
              : 'Try adjusting your search or filters'
            }
          </p>
          {activeTab === 'my-studios' && (
            <Button
              onClick={handleCreateStudio}
              color="primary"
              startContent={<Plus size={18} />}
            >
              Create Your First Studio
            </Button>
          )}
        </div>
      )}
    </div>
  );
};

export default StudioList;
