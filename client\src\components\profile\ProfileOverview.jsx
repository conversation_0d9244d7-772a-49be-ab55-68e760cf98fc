import React, { useState, useEffect, useContext } from 'react';
import { Card, CardBody, CardHeader, Button, Badge, Progress, Avatar, Chip, Divider } from '@heroui/react';
import { motion, AnimatePresence } from 'framer-motion';
import { UserContext } from '../../contexts/supabase-auth.context';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';

/**
 * Enhanced Profile Overview Component
 * 
 * Comprehensive profile dashboard providing:
 * - Professional profile summary
 * - Skills and achievements display
 * - Activity and analytics overview
 * - Quick action buttons
 * - Portfolio highlights
 * - Social connections summary
 */
const ProfileOverview = ({ userId, isOwnProfile = false, onEditProfile }) => {
  const { currentUser } = useContext(UserContext);
  
  // State management
  const [profileData, setProfileData] = useState(null);
  const [skillsData, setSkillsData] = useState([]);
  const [achievementsData, setAchievementsData] = useState([]);
  const [portfolioData, setPortfolioData] = useState([]);
  const [analyticsData, setAnalyticsData] = useState({});
  const [loading, setLoading] = useState(true);

  // Load profile overview data
  useEffect(() => {
    if (userId) {
      loadProfileOverview();
    }
  }, [userId]);

  const loadProfileOverview = async () => {
    try {
      setLoading(true);
      
      // Load profile data
      const { data: profile, error: profileError } = await supabase
        .from('users')
        .select(`
          id,
          display_name,
          professional_title,
          bio,
          avatar_url,
          location,
          website_url,
          availability_status,
          profile_views,
          created_at,
          stats,
          social_links,
          is_premium
        `)
        .eq('id', userId)
        .single();

      if (profileError) throw profileError;

      // Load skills data
      const { data: skills, error: skillsError } = await supabase
        .from('user_skills')
        .select('*')
        .eq('user_id', userId)
        .order('skill_level', { ascending: false })
        .limit(6);

      if (skillsError) console.error('Error loading skills:', skillsError);

      // Load achievements/certifications
      const achievements = profile?.certifications || [];
      const awards = profile?.awards || [];

      // Load portfolio items
      const { data: portfolio, error: portfolioError } = await supabase
        .from('portfolio_items')
        .select('*')
        .eq('user_id', userId)
        .eq('is_featured', true)
        .order('display_order', { ascending: true })
        .limit(3);

      if (portfolioError) console.error('Error loading portfolio:', portfolioError);

      // Fetch real analytics data from database
      const [connectionsResult, endorsementsResult, ratingsResult] = await Promise.allSettled([
        // Get connections count
        supabase
          .from('user_connections')
          .select('id', { count: 'exact', head: true })
          .eq('user_id', userId)
          .eq('status', 'accepted'),

        // Get skill endorsements count
        supabase
          .from('skill_endorsements')
          .select('id', { count: 'exact', head: true })
          .eq('endorsed_user_id', userId),

        // Get average rating from reviews
        supabase
          .from('user_reviews')
          .select('rating')
          .eq('reviewed_user_id', userId)
      ]);

      const connectionsCount = connectionsResult.status === 'fulfilled' ? connectionsResult.value.count || 0 : 0;
      const skillEndorsements = endorsementsResult.status === 'fulfilled' ? endorsementsResult.value.count || 0 : 0;

      let averageRating = 0;
      let responseRate = 95; // TODO: Calculate from message response data

      if (ratingsResult.status === 'fulfilled' && ratingsResult.value.data?.length > 0) {
        const ratings = ratingsResult.value.data;
        averageRating = ratings.reduce((sum, review) => sum + review.rating, 0) / ratings.length;
      }

      const analytics = {
        profileViews: profile?.profile_views || 0,
        connectionsCount,
        projectsCompleted: profile?.stats?.projects_completed || 0,
        skillEndorsements,
        responseRate,
        averageRating: Math.round(averageRating * 10) / 10 // Round to 1 decimal
      };

      setProfileData(profile);
      setSkillsData(skills || []);
      setAchievementsData([...achievements, ...awards]);
      setPortfolioData(portfolio || []);
      setAnalyticsData(analytics);
    } catch (error) {
      console.error('Error loading profile overview:', error);
      toast.error('Failed to load profile data');
    } finally {
      setLoading(false);
    }
  };

  // Get availability status color
  const getAvailabilityColor = (status) => {
    switch (status) {
      case 'available': return 'success';
      case 'busy': return 'warning';
      case 'unavailable': return 'danger';
      default: return 'default';
    }
  };

  // Get skill level color
  const getSkillLevelColor = (level) => {
    switch (level) {
      case 'expert': return 'danger';
      case 'advanced': return 'warning';
      case 'intermediate': return 'primary';
      case 'beginner': return 'default';
      default: return 'default';
    }
  };

  // Format member since date
  const formatMemberSince = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long'
    });
  };

  if (loading) {
    return (
      <div className="flex justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!profileData) {
    return (
      <Card>
        <CardBody className="text-center py-8">
          <span className="text-4xl mb-4 block">👤</span>
          <h3 className="text-lg font-semibold mb-2">Profile Not Found</h3>
          <p className="text-default-600">This user profile could not be loaded.</p>
        </CardBody>
      </Card>
    );
  }

  return (
    <div className="profile-overview space-y-6">
      {/* Profile Header Card */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <Card className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20">
          <CardBody className="p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <Avatar
                  src={profileData.avatar_url}
                  name={profileData.display_name}
                  size="lg"
                  className="w-16 h-16"
                />
                <div>
                  <div className="flex items-center gap-2 mb-1">
                    <h2 className="text-2xl font-bold">{profileData.display_name}</h2>
                    {profileData.is_premium && (
                      <Badge color="warning" variant="flat" size="sm">
                        ⭐ Premium
                      </Badge>
                    )}
                  </div>
                  <p className="text-lg text-default-600 mb-2">
                    {profileData.professional_title || 'Professional'}
                  </p>
                  <div className="flex items-center gap-3">
                    <Badge
                      color={getAvailabilityColor(profileData.availability_status)}
                      variant="flat"
                    >
                      {profileData.availability_status || 'available'}
                    </Badge>
                    {profileData.location && (
                      <span className="text-sm text-default-600">📍 {profileData.location}</span>
                    )}
                    <span className="text-sm text-default-600">
                      Member since {formatMemberSince(profileData.created_at)}
                    </span>
                  </div>
                </div>
              </div>
              
              {isOwnProfile && (
                <Button
                  color="primary"
                  variant="flat"
                  onClick={onEditProfile}
                  startContent={<span>✏️</span>}
                >
                  Edit Profile
                </Button>
              )}
            </div>
            
            {profileData.bio && (
              <div className="mt-4">
                <p className="text-default-700">{profileData.bio}</p>
              </div>
            )}
          </CardBody>
        </Card>
      </motion.div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <Card>
            <CardBody className="p-6 text-center">
              <div className="text-2xl font-bold text-primary mb-1">
                {analyticsData.profileViews}
              </div>
              <div className="text-sm text-default-600">Profile Views</div>
              <div className="text-xs text-default-500 mt-1">This month</div>
            </CardBody>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          <Card>
            <CardBody className="p-6 text-center">
              <div className="text-2xl font-bold text-success mb-1">
                {analyticsData.projectsCompleted}
              </div>
              <div className="text-sm text-default-600">Projects Completed</div>
              <div className="text-xs text-default-500 mt-1">All time</div>
            </CardBody>
          </Card>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.3 }}
        >
          <Card>
            <CardBody className="p-6 text-center">
              <div className="text-2xl font-bold text-warning mb-1">
                {analyticsData.averageRating}⭐
              </div>
              <div className="text-sm text-default-600">Average Rating</div>
              <div className="text-xs text-default-500 mt-1">From {analyticsData.connectionsCount} reviews</div>
            </CardBody>
          </Card>
        </motion.div>
      </div>

      {/* Skills & Portfolio Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Skills */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.4 }}
        >
          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold">Top Skills</h3>
            </CardHeader>
            <CardBody>
              {skillsData.length === 0 ? (
                <div className="text-center py-4">
                  <span className="text-2xl mb-2 block">🛠️</span>
                  <p className="text-default-600">No skills added yet</p>
                  {isOwnProfile && (
                    <Button size="sm" variant="flat" className="mt-2">
                      Add Skills
                    </Button>
                  )}
                </div>
              ) : (
                <div className="space-y-3">
                  {skillsData.map((skill, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{skill.skill_name}</span>
                        <Badge
                          color={getSkillLevelColor(skill.skill_level)}
                          variant="flat"
                          size="sm"
                        >
                          {skill.skill_level}
                        </Badge>
                      </div>
                      {skill.years_experience && (
                        <span className="text-sm text-default-600">
                          {skill.years_experience}y exp
                        </span>
                      )}
                    </div>
                  ))}
                  {skillsData.length >= 6 && (
                    <Button size="sm" variant="light" className="w-full">
                      View All Skills
                    </Button>
                  )}
                </div>
              )}
            </CardBody>
          </Card>
        </motion.div>

        {/* Featured Portfolio */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.5 }}
        >
          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold">Featured Work</h3>
            </CardHeader>
            <CardBody>
              {portfolioData.length === 0 ? (
                <div className="text-center py-4">
                  <span className="text-2xl mb-2 block">📁</span>
                  <p className="text-default-600">No portfolio items yet</p>
                  {isOwnProfile && (
                    <Button size="sm" variant="flat" className="mt-2">
                      Add Portfolio Item
                    </Button>
                  )}
                </div>
              ) : (
                <div className="space-y-3">
                  {portfolioData.map((item, index) => (
                    <div key={index} className="border rounded-lg p-3">
                      <div className="flex items-center gap-3">
                        {item.image_url && (
                          <img
                            src={item.image_url}
                            alt={item.title}
                            className="w-12 h-12 rounded object-cover"
                          />
                        )}
                        <div className="flex-1">
                          <h4 className="font-medium">{item.title}</h4>
                          <p className="text-sm text-default-600 line-clamp-1">
                            {item.description}
                          </p>
                          {item.technologies && (
                            <div className="flex gap-1 mt-1">
                              {item.technologies.slice(0, 3).map((tech, techIndex) => (
                                <Chip key={techIndex} size="sm" variant="flat">
                                  {tech}
                                </Chip>
                              ))}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                  <Button size="sm" variant="light" className="w-full">
                    View Full Portfolio
                  </Button>
                </div>
              )}
            </CardBody>
          </Card>
        </motion.div>
      </div>

      {/* Achievements */}
      {achievementsData.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.6 }}
        >
          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold">Achievements & Awards</h3>
            </CardHeader>
            <CardBody>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                {achievementsData.slice(0, 6).map((achievement, index) => (
                  <div key={index} className="flex items-center gap-2 p-2 rounded bg-default-50">
                    <span className="text-xl">🏆</span>
                    <span className="text-sm font-medium">{achievement.name || achievement}</span>
                  </div>
                ))}
              </div>
              {achievementsData.length > 6 && (
                <Button size="sm" variant="light" className="w-full mt-3">
                  View All Achievements
                </Button>
              )}
            </CardBody>
          </Card>
        </motion.div>
      )}
    </div>
  );
};

export default ProfileOverview;
