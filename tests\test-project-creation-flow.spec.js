import { test, expect } from '@playwright/test';

test.describe('Complete Royaltea User Flow Test', () => {
  test('Full authenticated user journey: Login → Project Creation → Agreement Generation', async ({ page }) => {
    console.log('🚀 Starting comprehensive Royaltea user flow test...');

    // Step 1: Navigate and Authenticate
    console.log('📍 Step 1: Authentication');
    await page.goto('https://royalty.technology');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);

    // Handle authentication - Use email/password instead of OAuth
    const loginButton = page.locator('button:has-text("Login"), button:has-text("Sign In"), a:has-text("Login"), a:has-text("Sign In")').first();
    if (await loginButton.isVisible()) {
      console.log('🔐 Login required, using email/password authentication...');
      await loginButton.click();
      await page.waitForTimeout(2000);

      // Wait for login form to load
      await page.waitForSelector('input[type="email"], input[placeholder*="email"]', { timeout: 10000 });

      // Fill login credentials
      const emailInput = page.locator('input[type="email"], input[placeholder*="email"]').first();
      const passwordInput = page.locator('input[type="password"], input[placeholder*="password"]').first();

      console.log('📝 Filling login credentials...');
      await emailInput.fill('<EMAIL>');
      await passwordInput.fill('TestPassword123!');

      // Take screenshot before submitting
      await page.screenshot({ path: 'test-results/00-login-form.png', fullPage: true });

      // Submit login form
      const submitButton = page.locator('button[type="submit"], button:has-text("Login"), button:has-text("Sign In")').first();
      await submitButton.click();

      // Wait for authentication to complete - check for redirect or dashboard
      console.log('⏳ Waiting for authentication to complete...');
      await page.waitForTimeout(8000);

      // Check if we're still on login page (authentication failed)
      const stillOnLogin = await page.locator('input[type="email"]').isVisible({ timeout: 3000 });
      if (stillOnLogin) {
        console.log('❌ Authentication may have failed, still on login page');
        // Try to find error messages
        const errorMessage = page.locator('.error, .alert, [role="alert"]');
        if (await errorMessage.isVisible()) {
          const errorText = await errorMessage.textContent();
          console.log(`❌ Error message: ${errorText}`);
        }
      } else {
        console.log('✅ Authentication completed successfully');
      }
    } else {
      console.log('✅ Already authenticated or no login required');
    }

    // Take screenshot after authentication
    await page.screenshot({ path: 'test-results/01-authenticated.png', fullPage: true });
    console.log(`🌐 Current URL after auth: ${page.url()}`);

    // Step 2: Navigate to Project Creation
    console.log('📍 Step 2: Navigate to Project Creation');

    // Look for navigation to project creation
    const startButton = page.locator('button:has-text("Start"), a:has-text("Start"), [data-testid="start-button"]').first();
    const createProjectButton = page.locator('button:has-text("Create Project"), button:has-text("New Project"), a:has-text("Create Project")').first();
    const plusButton = page.locator('button:has-text("+"), [aria-label*="create"], [aria-label*="add"]').first();

    let navigationSuccess = false;

    if (await startButton.isVisible()) {
      console.log('✅ Found Start button, clicking...');
      await startButton.click();
      navigationSuccess = true;
    } else if (await createProjectButton.isVisible()) {
      console.log('✅ Found Create Project button, clicking...');
      await createProjectButton.click();
      navigationSuccess = true;
    } else if (await plusButton.isVisible()) {
      console.log('✅ Found Plus button, clicking...');
      await plusButton.click();
      navigationSuccess = true;
    } else {
      console.log('🔍 Searching navigation menu for project creation...');
      const navItems = page.locator('nav a, .nav a, [role="navigation"] a, header a');
      const navCount = await navItems.count();
      console.log(`Found ${navCount} navigation items`);

      for (let i = 0; i < navCount; i++) {
        const navItem = navItems.nth(i);
        const text = await navItem.textContent();
        console.log(`Nav item ${i}: ${text}`);
        if (text && (text.toLowerCase().includes('start') || text.toLowerCase().includes('project') || text.toLowerCase().includes('create'))) {
          console.log(`✅ Clicking navigation item: ${text}`);
          await navItem.click();
          navigationSuccess = true;
          break;
        }
      }
    }

    await page.waitForTimeout(3000);
    await page.screenshot({ path: 'test-results/02-after-navigation.png', fullPage: true });
    console.log(`🌐 URL after navigation: ${page.url()}`);

    if (!navigationSuccess) {
      console.log('❌ Could not find navigation to project creation');
      // Try multiple direct URL navigation options as fallback
      const directUrls = [
        'https://royalty.technology/project/create',
        'https://royalty.technology/projects/new',
        'https://royalty.technology/start',
        'https://royalty.technology/dashboard'
      ];

      for (const url of directUrls) {
        console.log(`🔄 Trying direct navigation to: ${url}`);
        await page.goto(url);
        await page.waitForTimeout(3000);

        // Check if we found project creation elements
        const hasProjectElements = await page.locator('h1:has-text("Project"), h2:has-text("Project"), input[placeholder*="project" i]').isVisible({ timeout: 2000 });
        if (hasProjectElements) {
          console.log(`✅ Found project elements at: ${url}`);
          navigationSuccess = true;
          break;
        }
      }
    }
    
    // Look for navigation to Start page or project creation
    console.log('🚀 Looking for Start page or project creation...');

    // Step 3: Project Type Selection
    console.log('📍 Step 3: Project Type Selection');

    // Look for project type selection
    const projectTypeButtons = page.locator('button:has-text("Software"), button:has-text("Creative"), button:has-text("Business"), [data-testid*="project-type"]');
    if (await projectTypeButtons.first().isVisible()) {
      console.log('✅ Found project type selection, selecting first option...');
      await projectTypeButtons.first().click();
      await page.waitForTimeout(1000);

      // Look for Next button
      const nextButton = page.locator('button:has-text("Next"), button:has-text("Continue")');
      if (await nextButton.isVisible()) {
        console.log('✅ Proceeding to next step...');
        await nextButton.click();
        await page.waitForTimeout(2000);
      }
    } else {
      console.log('⚠️ No project type selection found, may already be on project basics');
    }

    await page.screenshot({ path: 'test-results/03-project-type-selection.png', fullPage: true });

    // Step 4: Test Company Information Fields
    console.log('📍 Step 4: Company Information Fields Testing');

    // Check for company information section
    const companySection = page.locator('h3:has-text("Company Information"), div:has-text("Company Information"), [data-testid*="company"]');
    const companyInfoVisible = await companySection.isVisible();
    console.log(`✅ Company Information section visible: ${companyInfoVisible}`);

    // Test all company fields with multiple selectors
    const fieldTests = [
      { name: 'Company Name', selectors: ['input[placeholder*="company name" i]', 'label:has-text("Company Name") + input', '[data-testid*="company-name"]', 'input[name*="company_name"]'] },
      { name: 'Company Address', selectors: ['input[placeholder*="address" i]', 'label:has-text("Address") + input', '[data-testid*="address"]', 'input[name*="company_address"]'] },
      { name: 'State/Province', selectors: ['input[placeholder*="state" i]', 'label:has-text("State") + input', '[data-testid*="state"]', 'input[name*="company_state"]'] },
      { name: 'County/Region', selectors: ['input[placeholder*="county" i]', 'label:has-text("County") + input', '[data-testid*="county"]', 'input[name*="company_county"]'] },
      { name: 'City', selectors: ['input[placeholder*="city" i]', 'label:has-text("City") + input', '[data-testid*="city"]', 'input[name*="company_city"]'] },
      { name: 'Contact Email', selectors: ['input[placeholder*="contact email" i]', 'label:has-text("Contact Email") + input', '[data-testid*="contact-email"]', 'input[name*="contact_email"]'] },
      { name: 'Authorized Signer Name', selectors: ['input[placeholder*="signer" i]', 'label:has-text("Authorized Signer") + input', '[data-testid*="signer"]', 'input[name*="signer_name"]'] },
      { name: 'Signer Title', selectors: ['input[placeholder*="title" i]', 'label:has-text("Title") + input', '[data-testid*="title"]', 'input[name*="signer_title"]'] }
    ];

    const visibleFields = [];
    const testData = {
      'Company Name': 'Test Company LLC',
      'Company Address': '123 Test Street',
      'State/Province': 'California',
      'County/Region': 'Los Angeles',
      'City': 'Los Angeles',
      'Contact Email': '<EMAIL>',
      'Authorized Signer Name': 'John Doe',
      'Signer Title': 'CEO'
    };

    for (const field of fieldTests) {
      let fieldFound = false;
      for (const selector of field.selectors) {
        const fieldElement = page.locator(selector).first();
        if (await fieldElement.isVisible()) {
          console.log(`✅ ${field.name} field is VISIBLE (selector: ${selector})`);
          visibleFields.push(field.name);

          // Fill the field with test data
          if (testData[field.name]) {
            await fieldElement.fill(testData[field.name]);
            console.log(`📝 Filled ${field.name} with: ${testData[field.name]}`);
          }

          fieldFound = true;
          break;
        }
      }
      if (!fieldFound) {
        console.log(`❌ ${field.name} field is NOT visible`);
      }
    }

    console.log(`📊 Summary: ${visibleFields.length}/${fieldTests.length} company fields are visible`);
    console.log(`✅ Visible fields: ${visibleFields.join(', ')}`);

    await page.screenshot({ path: 'test-results/04-company-fields-filled.png', fullPage: true });

    // Step 5: Continue with Project Creation
    console.log('📍 Step 5: Continue Project Creation Process');

    // Fill basic project information if visible
    const projectNameField = page.locator('input[placeholder*="project name" i], input[name*="project_name"], label:has-text("Project Name") + input').first();
    const projectDescField = page.locator('textarea[placeholder*="description" i], textarea[name*="description"], label:has-text("Description") + textarea').first();

    if (await projectNameField.isVisible()) {
      await projectNameField.fill('Test Legal Agreement Project');
      console.log('📝 Filled project name');
    }

    if (await projectDescField.isVisible()) {
      await projectDescField.fill('This is a test project to validate the legal agreement generation system.');
      console.log('📝 Filled project description');
    }

    // Look for Continue/Next/Save buttons
    const continueButtons = page.locator('button:has-text("Continue"), button:has-text("Next"), button:has-text("Save"), button:has-text("Create Project")');
    const continueButtonCount = await continueButtons.count();

    if (continueButtonCount > 0) {
      console.log(`✅ Found ${continueButtonCount} continue button(s), clicking first one...`);
      await continueButtons.first().click();
      await page.waitForTimeout(3000);
      await page.screenshot({ path: 'test-results/05-after-continue.png', fullPage: true });
    }

    // Step 6: Test Agreement Generation (if we reach that step)
    console.log('📍 Step 6: Agreement Generation Testing');

    // Look for agreement generation or review step
    const agreementSection = page.locator('h1:has-text("Agreement"), h2:has-text("Agreement"), h3:has-text("Agreement"), div:has-text("Contributor Agreement")');
    const agreementVisible = await agreementSection.isVisible();
    console.log(`✅ Agreement section visible: ${agreementVisible}`);

    if (agreementVisible) {
      // Check if agreement content is populated
      const agreementContent = page.locator('pre, .agreement-content, [data-testid*="agreement"]');
      if (await agreementContent.isVisible()) {
        const content = await agreementContent.textContent();
        const hasCompanyName = content.includes('Test Company LLC');
        const hasContactEmail = content.includes('<EMAIL>');
        const hasSignerName = content.includes('John Doe');

        console.log(`📄 Agreement contains company name: ${hasCompanyName}`);
        console.log(`📄 Agreement contains contact email: ${hasContactEmail}`);
        console.log(`📄 Agreement contains signer name: ${hasSignerName}`);

        if (hasCompanyName && hasContactEmail && hasSignerName) {
          console.log('🎉 SUCCESS: Agreement generation working with company data!');
        } else {
          console.log('⚠️ WARNING: Agreement may not contain all company information');
        }
      }
    }

    // Final comprehensive screenshot
    await page.screenshot({ path: 'test-results/06-final-state.png', fullPage: true });

    // Step 7: Summary and Analysis
    console.log('📍 Step 7: Test Summary');
    console.log(`🌐 Final URL: ${page.url()}`);
    console.log(`📊 Company fields found: ${visibleFields.length}/${fieldTests.length}`);
    console.log(`📄 Agreement generation tested: ${agreementVisible}`);

    // Check for any styling or UX issues
    const pageContent = await page.content();
    const hasErrors = pageContent.includes('error') || pageContent.includes('Error') || pageContent.includes('404') || pageContent.includes('500');
    const hasLoadingIssues = pageContent.includes('loading') || pageContent.includes('Loading');

    console.log(`⚠️ Page has errors: ${hasErrors}`);
    console.log(`⏳ Page has loading issues: ${hasLoadingIssues}`);

    if (visibleFields.length >= 6) {
      console.log('🎉 OVERALL SUCCESS: Company information system is working!');
    } else {
      console.log('❌ OVERALL FAILURE: Company information system needs attention');
    }
  });
});
