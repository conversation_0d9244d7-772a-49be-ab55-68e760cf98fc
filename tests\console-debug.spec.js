import { test, expect } from '@playwright/test';

test.describe('Console Debug', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to login page and authenticate
    await page.goto('https://royalty.technology/login');
    await page.waitForLoadState('networkidle');
    
    // Login with test credentials
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'TestPassword123!');
    await page.click('button[type="submit"]');
    
    // Wait for authentication to complete
    await page.waitForURL('**/dashboard', { timeout: 10000 });
    await page.waitForLoadState('networkidle');
  });

  test('Check console errors during agreement generation', async ({ page }) => {
    console.log('🔍 Checking console errors...');

    // Collect console messages
    const consoleMessages = [];
    page.on('console', msg => {
      consoleMessages.push({
        type: msg.type(),
        text: msg.text(),
        location: msg.location()
      });
      
      if (msg.type() === 'error') {
        console.log('❌ Console Error:', msg.text());
      } else if (msg.text().includes('Agreement') || msg.text().includes('Template') || msg.text().includes('Generate')) {
        console.log('📝 Agreement Log:', msg.text());
      }
    });

    // Navigate to project creation
    await page.goto('https://royalty.technology/project/create');
    await page.waitForLoadState('networkidle');
    
    // Fill basic project information
    await page.fill('input[placeholder="Enter project name"]', 'Console Debug Project');
    await page.fill('textarea[placeholder="Describe your project"]', 'Test project for console debugging');
    
    // Navigate through wizard steps to reach Review & Agreement
    for (let i = 0; i < 6; i++) {
      await page.waitForTimeout(1000);
      
      const nextBtn = page.locator('button:has-text("Next"), button:has-text("Continue")').first();
      if (await nextBtn.isVisible()) {
        await nextBtn.click({ force: true });
        await page.waitForTimeout(1500);
      }
    }
    
    // Wait for Review & Agreement page to load
    await page.waitForTimeout(3000);
    
    // Check if we're on the Review & Agreement step
    const reviewTitle = page.locator('h2:has-text("Review & Agreement")');
    await expect(reviewTitle).toBeVisible({ timeout: 10000 });
    console.log('✓ Reached Review & Agreement step');
    
    // Wait for agreement generation
    await page.waitForTimeout(10000);
    
    // Print all console messages
    console.log('\n📋 All Console Messages:');
    consoleMessages.forEach((msg, index) => {
      console.log(`${index + 1}. [${msg.type.toUpperCase()}] ${msg.text}`);
      if (msg.location) {
        console.log(`   Location: ${msg.location.url}:${msg.location.lineNumber}:${msg.location.columnNumber}`);
      }
    });

    // Check agreement content
    const agreementContent = await page.locator('.agreement-content, .agreement-preview').first().textContent();
    console.log('\n📄 Agreement Content Length:', agreementContent?.length || 0);
    console.log('📄 Agreement Content Preview:', agreementContent?.substring(0, 300) || 'No content');

    // Take a screenshot
    await page.screenshot({ path: 'console-debug.png', fullPage: true });
    console.log('📸 Screenshot saved as console-debug.png');
  });
});
