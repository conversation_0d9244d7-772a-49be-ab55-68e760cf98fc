import React, { useState, useContext } from 'react';
import { Card, CardBody, Button, Input, Textarea, Select, SelectItem, Switch, Chip, Divider } from '@heroui/react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { UserContext } from '../../contexts/supabase-auth.context.jsx';
import { supabase } from '../../utils/supabase/supabase.utils';
import { toast } from 'react-hot-toast';
import { Building2, Users, Globe, Lock } from 'lucide-react';

const StudioCreationWizard = ({ onComplete, onCancel }) => {
  const navigate = useNavigate();
  const { currentUser } = useContext(UserContext);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    studio_type: 'emerging',
    industry: '',
    is_public: true,
    max_members: 10,
    business_model: 'collaborative',
    tags: []
  });

  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});

  // Validation function
  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Studio name is required';
    } else if (formData.name.trim().length < 3) {
      newErrors.name = 'Studio name must be at least 3 characters';
    } else if (formData.name.trim().length > 50) {
      newErrors.name = 'Studio name must be less than 50 characters';
    }

    if (formData.description.trim().length > 500) {
      newErrors.description = 'Description must be less than 500 characters';
    }

    if (!formData.industry.trim()) {
      newErrors.industry = 'Industry is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    const loadingToastId = toast.loading('Creating your studio...');

    try {
      // Create studio in database
      const { data: studio, error: studioError } = await supabase
        .from('studios')
        .insert([
          {
            name: formData.name.trim(),
            description: formData.description.trim(),
            studio_type: formData.studio_type,
            industry: formData.industry.trim(),
            is_public: formData.is_public,
            max_members: formData.max_members,
            business_model: formData.business_model,
            owner_id: currentUser.id,
            created_by: currentUser.id
          }
        ])
        .select()
        .single();

      if (studioError) {
        throw new Error(`Studio creation failed: ${studioError.message}`);
      }

      // Add owner as member
      const { error: memberError } = await supabase
        .from('studio_members')
        .insert([
          {
            studio_id: studio.id,
            user_id: currentUser.id,
            role: 'owner',
            status: 'active',
            joined_at: new Date().toISOString()
          }
        ]);

      if (memberError) {
        console.error('Error adding owner as member:', memberError);
        // Don't throw here as studio was created successfully
      }

      toast.success('Studio created successfully!', { id: loadingToastId });
      
      if (onComplete) {
        onComplete(studio);
      } else {
        navigate(`/studios/${studio.id}`);
      }

    } catch (error) {
      console.error('Error creating studio:', error);
      toast.error(`Failed to create studio: ${error.message}`, { id: loadingToastId });
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    } else {
      navigate('/studios');
    }
  };

  return (
    <div className="studio-creation-wizard">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-4xl"
      >
        <div className="studio-creation-card">
          <div className="studio-creation-header">
            <div className="flex items-center justify-center mb-4">
              <div className="studio-creation-section-icon">
                <Building2 className="w-6 h-6" />
              </div>
              <h1 className="wizard-heading-responsive ml-3">Create Your Studio</h1>
            </div>
            <p className="wizard-text-responsive text-default-500">
              Set up your creative studio and start building amazing projects together
            </p>
          </div>

          <form onSubmit={handleSubmit} className="studio-creation-form">
            {/* Basic Information Section */}
            <div className="studio-creation-section">
              <div className="studio-creation-section-header">
                <div className="studio-creation-section-icon">
                  <Building2 className="w-5 h-5" />
                </div>
                <h3 className="studio-creation-section-title">Basic Information</h3>
              </div>

              <div className="wizard-space-y">
                <div className="wizard-form-field">
                  <Input
                    label="Studio Name"
                    placeholder="Enter your studio name"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    isInvalid={!!errors.name}
                    errorMessage={errors.name}
                    description="Choose a unique name that represents your studio"
                    required
                    size="lg"
                    variant="bordered"
                    classNames={{
                      input: "text-lg",
                      inputWrapper: "h-14"
                    }}
                  />
                </div>

                <div className="wizard-form-field">
                  <Textarea
                    label="Description"
                    placeholder="Describe your studio's mission, goals, and what makes it unique"
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    isInvalid={!!errors.description}
                    errorMessage={errors.description}
                    description={`${formData.description.length}/500 characters`}
                    rows={4}
                    variant="bordered"
                    classNames={{
                      input: "text-base"
                    }}
                  />
                </div>

                <div className="wizard-form-field">
                  <Input
                    label="Industry"
                    placeholder="e.g., Software Development, Gaming, Film Production, Music"
                    value={formData.industry}
                    onChange={(e) => setFormData({ ...formData, industry: e.target.value })}
                    isInvalid={!!errors.industry}
                    errorMessage={errors.industry}
                    description="What industry does your studio operate in?"
                    required
                    size="lg"
                    variant="bordered"
                    classNames={{
                      inputWrapper: "h-14"
                    }}
                  />
                </div>
              </div>
            </div>

            <Divider className="my-8" />

            {/* Studio Configuration Section */}
            <div className="studio-creation-section">
              <div className="studio-creation-section-header">
                <div className="studio-creation-section-icon">
                  <Users className="w-5 h-5" />
                </div>
                <h3 className="studio-creation-section-title">Studio Configuration</h3>
              </div>

              <div className="wizard-space-y">
                <div className="wizard-form-field">
                  <Select
                    label="Studio Type"
                    placeholder="Select your studio type"
                    selectedKeys={[formData.studio_type]}
                    onSelectionChange={(keys) => {
                      const selectedKey = Array.from(keys)[0];
                      setFormData({ ...formData, studio_type: selectedKey });
                    }}
                    description="Choose the type that best describes your studio's current stage"
                    size="lg"
                    variant="bordered"
                    classNames={{
                      trigger: "h-14"
                    }}
                  >
                    <SelectItem key="emerging" value="emerging">
                      🌱 Emerging - New studio building its foundation and exploring opportunities
                    </SelectItem>
                    <SelectItem key="established" value="established">
                      🏰 Established - Mature studio with proven track record and steady workflow
                    </SelectItem>
                    <SelectItem key="solo" value="solo">
                      ⚔️ Solo - Individual creator or freelancer working independently
                    </SelectItem>
                  </Select>
                </div>

                <div className="wizard-form-field">
                  <Select
                    label="Business Model"
                    placeholder="Select your business approach"
                    selectedKeys={[formData.business_model]}
                    onSelectionChange={(keys) => {
                      const selectedKey = Array.from(keys)[0];
                      setFormData({ ...formData, business_model: selectedKey });
                    }}
                    description="How does your studio approach collaboration and revenue?"
                    size="lg"
                    variant="bordered"
                    classNames={{
                      trigger: "h-14"
                    }}
                  >
                    <SelectItem key="collaborative" value="collaborative">
                      🤝 Collaborative - Shared ownership and revenue distribution
                    </SelectItem>
                    <SelectItem key="client_service" value="client_service">
                      💼 Client Service - Traditional client work with fixed rates
                    </SelectItem>
                    <SelectItem key="product_focused" value="product_focused">
                      🚀 Product Focused - Building and selling your own products
                    </SelectItem>
                    <SelectItem key="hybrid" value="hybrid">
                      ⚡ Hybrid - Mix of collaborative projects and client work
                    </SelectItem>
                  </Select>
                </div>

                <div className="wizard-form-field">
                  <Input
                    label="Maximum Members"
                    type="number"
                    placeholder="10"
                    value={formData.max_members.toString()}
                    onChange={(e) => setFormData({ ...formData, max_members: parseInt(e.target.value) || 10 })}
                    description="How many people can join your studio? (You can change this later)"
                    min="1"
                    max="100"
                    size="lg"
                    variant="bordered"
                    classNames={{
                      inputWrapper: "h-14"
                    }}
                  />
                </div>
              </div>
            </div>

            <Divider className="my-8" />

            {/* Privacy & Visibility Section */}
            <div className="studio-creation-section">
              <div className="studio-creation-section-header">
                <div className="studio-creation-section-icon">
                  <Globe className="w-5 h-5" />
                </div>
                <h3 className="studio-creation-section-title">Privacy & Visibility</h3>
              </div>

              <div className="wizard-space-y">
                <div className="flex items-center justify-between p-6 bg-default-50 rounded-xl border border-default-200">
                  <div className="flex items-center">
                    {formData.is_public ? (
                      <Globe className="w-6 h-6 text-success mr-4" />
                    ) : (
                      <Lock className="w-6 h-6 text-warning mr-4" />
                    )}
                    <div>
                      <p className="font-semibold text-lg">
                        {formData.is_public ? 'Public Studio' : 'Private Studio'}
                      </p>
                      <p className="text-sm text-default-500 mt-1">
                        {formData.is_public
                          ? 'Anyone can discover and request to join your studio'
                          : 'Only invited members can see and join your studio'
                        }
                      </p>
                    </div>
                  </div>
                  <Switch
                    isSelected={formData.is_public}
                    onValueChange={(value) => setFormData({ ...formData, is_public: value })}
                    color="primary"
                    size="lg"
                  />
                </div>

                {formData.is_public && (
                  <div className="p-6 bg-primary-50 rounded-xl border border-primary-200">
                    <div className="flex items-start">
                      <Globe className="w-6 h-6 text-primary mr-3 mt-1" />
                      <div>
                        <p className="font-semibold text-primary text-lg mb-2">Public Studio Benefits</p>
                        <ul className="text-sm text-primary-700 space-y-2">
                          <li className="flex items-center">
                            <span className="mr-2">•</span>
                            Attract talented collaborators from the community
                          </li>
                          <li className="flex items-center">
                            <span className="mr-2">•</span>
                            Showcase your studio's work and achievements
                          </li>
                          <li className="flex items-center">
                            <span className="mr-2">•</span>
                            Participate in public project opportunities
                          </li>
                        </ul>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </form>

          {/* Action Buttons */}
          <div className="studio-creation-actions">
            <Button
              variant="flat"
              onClick={handleCancel}
              disabled={loading}
              size="lg"
              className="min-w-[120px]"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              color="primary"
              isLoading={loading}
              disabled={!formData.name.trim() || !formData.industry.trim()}
              size="lg"
              className="min-w-[160px]"
              onClick={handleSubmit}
            >
              {loading ? 'Creating...' : 'Create Studio'}
            </Button>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default StudioCreationWizard;
