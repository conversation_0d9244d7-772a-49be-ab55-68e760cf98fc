import { test, expect } from '@playwright/test';

test.describe('Project Wizard Styling Tests', () => {
  test.beforeEach(async ({ page }) => {
    console.log('🚀 Starting Project Wizard Styling Test...');
    
    // Navigate to the production site
    await page.goto('https://royalty.technology');
    
    // Wait for page to load
    await page.waitForLoadState('networkidle');
    
    // Login with test credentials
    console.log('🔐 Logging in...');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'TestPassword123!');
    await page.click('button[type="submit"]');
    
    // Wait for dashboard to load
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    // Navigate to project creation
    console.log('📝 Navigating to project creation...');
    await page.click('text=Start');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(1000);
  });

  test('Check Project Wizard Visual Improvements', async ({ page }) => {
    console.log('🎨 Testing project wizard styling improvements...');

    // Take screenshot of the wizard
    await page.screenshot({
      path: 'test-results/project-wizard-styling.png',
      fullPage: true
    });

    // Check for duplicate step labels (should be fixed)
    const stepLabels = await page.locator('text="Project Basics"').count();
    console.log(`📊 "Project Basics" labels found: ${stepLabels}`);

    // Check for proper form spacing - look for any form elements
    const formInputs = page.locator('input, select, textarea');
    const inputCount = await formInputs.count();
    console.log(`📝 Form elements found: ${inputCount}`);
    expect(inputCount).toBeGreaterThan(0);

    // Check company information section styling
    const companySection = page.locator('text="Company Information"');
    const companyVisible = await companySection.isVisible();
    console.log(`🏢 Company Information section visible: ${companyVisible}`);

    // Check for debug elements (should be removed)
    const debugElements = page.locator('[style*="background-color: red"], [style*="backgroundColor: red"]');
    const debugCount = await debugElements.count();
    console.log(`🐛 Debug elements found: ${debugCount}`);
    expect(debugCount).toBe(0); // Should be 0 after cleanup

    // Check thumbnail upload section
    const thumbnailSection = page.locator('text="Project Thumbnail"');
    const thumbnailVisible = await thumbnailSection.isVisible();
    console.log(`🖼️ Thumbnail section visible: ${thumbnailVisible}`);

    // Check privacy section
    const privacySection = page.locator('text="Project Privacy"');
    const privacyVisible = await privacySection.isVisible();
    console.log(`🔒 Privacy section visible: ${privacyVisible}`);

    // Test form interaction - try to find project name input
    const projectNameInput = page.locator('input').first();
    if (await projectNameInput.isVisible()) {
      await projectNameInput.fill('Test Styling Project');
      console.log('📝 Successfully filled first input field');
    }

    // Check for proper styling classes
    const styledElements = page.locator('[class*="space-y"], [class*="gap-"], [class*="grid"], [class*="flex"]');
    const styledCount = await styledElements.count();
    console.log(`🎨 Elements with proper styling classes: ${styledCount}`);
    expect(styledCount).toBeGreaterThan(0);

    // Take final screenshot
    await page.screenshot({
      path: 'test-results/project-wizard-final.png',
      fullPage: true
    });

    console.log('✅ Project wizard styling test completed successfully!');
  });

  test('Check Visual Hierarchy and Spacing', async ({ page }) => {
    console.log('📐 Testing visual hierarchy and spacing...');
    
    // Check for proper section spacing
    const sections = page.locator('[class*="space-y"], [class*="gap-"]');
    const sectionCount = await sections.count();
    console.log(`📏 Sections with proper spacing: ${sectionCount}`);
    expect(sectionCount).toBeGreaterThan(0);
    
    // Check for proper grid layouts
    const gridElements = page.locator('[class*="grid"], [class*="flex"]');
    const gridCount = await gridElements.count();
    console.log(`🔲 Grid/flex elements: ${gridCount}`);
    expect(gridCount).toBeGreaterThan(0);
    
    // Check for proper text hierarchy
    const headings = page.locator('h1, h2, h3, h4, h5, h6');
    const headingCount = await headings.count();
    console.log(`📝 Headings found: ${headingCount}`);
    expect(headingCount).toBeGreaterThan(0);
    
    // Check for consistent button styling
    const buttons = page.locator('button');
    const buttonCount = await buttons.count();
    console.log(`🔘 Buttons found: ${buttonCount}`);
    expect(buttonCount).toBeGreaterThan(0);
    
    console.log('✅ Visual hierarchy test completed!');
  });
});
