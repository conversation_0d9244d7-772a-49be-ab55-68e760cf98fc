import { test, expect } from '@playwright/test';

test.describe('Supabase Connection Test', () => {
  test('Check if Supabase authentication is working', async ({ page }) => {
    console.log('🚀 Testing Supabase connection...');
    
    // Collect all console messages
    const consoleMessages = [];
    const jsErrors = [];
    
    page.on('console', msg => {
      consoleMessages.push({
        type: msg.type(),
        text: msg.text()
      });
      
      if (msg.type() === 'error') {
        jsErrors.push(msg.text());
      }
    });

    // Navigate to the site
    await page.goto('https://royalty.technology');
    
    // Wait for page to load completely
    await page.waitForTimeout(10000); // Wait longer for auth to complete
    
    // Check for Supabase-related messages
    const supabaseMessages = consoleMessages.filter(msg => 
      msg.text.toLowerCase().includes('supabase') ||
      msg.text.toLowerCase().includes('auth') ||
      msg.text.toLowerCase().includes('session') ||
      msg.text.toLowerCase().includes('user')
    );
    
    console.log('📊 Total Console Messages:', consoleMessages.length);
    console.log('🔐 Supabase/Auth Messages:', supabaseMessages.length);
    console.log('🚨 JavaScript Errors:', jsErrors.length);
    
    // Print Supabase-related messages
    if (supabaseMessages.length > 0) {
      console.log('\n🔐 Supabase/Auth Messages:');
      supabaseMessages.forEach((msg, index) => {
        console.log(`  ${index + 1}. [${msg.type}] ${msg.text}`);
      });
    }
    
    // Print JavaScript errors
    if (jsErrors.length > 0) {
      console.log('\n🚨 JavaScript Errors:');
      jsErrors.forEach((error, index) => {
        console.log(`  ${index + 1}. ${error}`);
      });
    }
    
    // Check if UserContext debug messages appear
    const userContextMessages = consoleMessages.filter(msg => 
      msg.text.includes('APP.JSX') ||
      msg.text.includes('isLoading state changed') ||
      msg.text.includes('Auth state changed')
    );
    
    console.log('\n🔍 UserContext Debug Messages:', userContextMessages.length);
    if (userContextMessages.length > 0) {
      userContextMessages.forEach((msg, index) => {
        console.log(`  ${index + 1}. [${msg.type}] ${msg.text}`);
      });
    }
    
    // Try to manually test Supabase connection
    const supabaseTest = await page.evaluate(async () => {
      try {
        // Check if Supabase is available
        if (typeof window.supabase === 'undefined') {
          return { success: false, error: 'Supabase not available on window' };
        }
        
        // Try to get session
        const { data, error } = await window.supabase.auth.getSession();
        
        return { 
          success: true, 
          hasSession: !!data?.session,
          user: data?.session?.user?.email || null,
          error: error?.message || null
        };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });
    
    console.log('\n🧪 Manual Supabase Test:', supabaseTest);
    
    // Check current page state
    const pageState = await page.evaluate(() => {
      return {
        title: document.title,
        hasRootContent: !!document.querySelector('#root'),
        rootContent: document.querySelector('#root')?.innerHTML?.substring(0, 200) + '...',
        isLoading: document.querySelector('[data-testid="simple-loading"]') !== null
      };
    });
    
    console.log('\n📄 Page State:', pageState);
    
    // Print all console messages for debugging
    console.log('\n📋 All Console Messages:');
    consoleMessages.forEach((msg, index) => {
      console.log(`  ${index + 1}. [${msg.type}] ${msg.text}`);
    });
    
    expect(true).toBeTruthy();
  });
});
