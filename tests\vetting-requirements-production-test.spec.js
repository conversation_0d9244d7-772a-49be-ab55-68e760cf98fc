// Vetting Requirements Production Test
// Tests the complete vetting requirements integration in production

import { test, expect } from '@playwright/test';

test.describe('Vetting Requirements Integration - Production Test', () => {
  const PRODUCTION_URL = 'https://royalty.technology';
  const TEST_EMAIL = '<EMAIL>';
  const TEST_PASSWORD = 'TestPassword123!';

  test.beforeEach(async ({ page }) => {
    // Navigate to production site
    await page.goto(PRODUCTION_URL);
    await page.waitForLoadState('networkidle');
  });

  test('should complete full vetting requirements workflow', async ({ page }) => {
    console.log('🧪 Testing Vetting Requirements Integration in Production');
    
    // Step 1: Login
    console.log('Step 1: Authenticating user...');
    await page.fill('input[type="email"]', TEST_EMAIL);
    await page.fill('input[type="password"]', TEST_PASSWORD);
    await page.click('button[type="submit"]');
    await page.waitForLoadState('networkidle');
    
    // Verify we're logged in
    await expect(page.locator('text=Dashboard')).toBeVisible({ timeout: 10000 });
    console.log('✅ Authentication successful');

    // Step 2: Navigate to Earn section (Gigwork)
    console.log('Step 2: Navigating to Earn section...');
    await page.click('text=Earn');
    await page.waitForLoadState('networkidle');
    
    // Step 3: Create a new gigwork request with vetting requirements
    console.log('Step 3: Creating gigwork request with vetting requirements...');
    await page.click('text=Create Request', { timeout: 10000 });
    await page.waitForLoadState('networkidle');
    
    // Fill out Step 1: Basic Information
    console.log('  - Filling Step 1: Basic Information');
    await page.fill('input[placeholder*="title"], input[name="title"]', 'Test Vetting Requirements Project');
    await page.fill('textarea[placeholder*="description"], textarea[name="description"]', 'This is a test project to validate vetting requirements functionality in production.');
    await page.selectOption('select[name="category"]', 'development');
    await page.fill('input[name="budget"]', '5000');
    await page.click('button:has-text("Next")');
    await page.waitForTimeout(1000);

    // Fill out Step 2: Project Details  
    console.log('  - Filling Step 2: Project Details');
    await page.fill('input[name="duration"]', '30');
    await page.selectOption('select[name="urgency"]', 'medium');
    await page.fill('textarea[name="requirements"]', 'Must have experience with React and Node.js');
    await page.click('button:has-text("Next")');
    await page.waitForTimeout(1000);

    // Fill out Step 3: Vetting Requirements (NEW FUNCTIONALITY)
    console.log('  - Filling Step 3: Vetting Requirements (TESTING NEW FEATURE)');
    
    // Verify vetting requirements step is visible
    await expect(page.locator('text=Vetting Requirements')).toBeVisible({ timeout: 5000 });
    console.log('    ✅ Vetting Requirements step is visible');
    
    // Set minimum vetting level
    await page.selectOption('select[name="minimum_vetting_level"]', '2'); // Peer Verified
    console.log('    ✅ Set minimum vetting level to Peer Verified');
    
    // Set preferred vetting level  
    await page.selectOption('select[name="preferred_vetting_level"]', '3'); // Project Verified
    console.log('    ✅ Set preferred vetting level to Project Verified');
    
    // Add required certifications
    const certificationInput = page.locator('input[placeholder*="certification"], input[name*="certification"]');
    if (await certificationInput.isVisible()) {
      await certificationInput.fill('React Development');
      await page.keyboard.press('Enter');
      await certificationInput.fill('Node.js Backend');
      await page.keyboard.press('Enter');
      console.log('    ✅ Added required certifications');
    }
    
    await page.click('button:has-text("Next")');
    await page.waitForTimeout(1000);

    // Fill out Step 4: Contact & Timeline
    console.log('  - Filling Step 4: Contact & Timeline');
    await page.fill('input[name="contact_email"]', TEST_EMAIL);
    await page.fill('input[name="deadline"]', '2025-02-15');
    await page.click('button:has-text("Next")');
    await page.waitForTimeout(1000);

    // Step 5: Review and Submit
    console.log('  - Step 5: Review and Submit');
    
    // Verify vetting requirements are shown in review
    await expect(page.locator('text=Minimum Vetting Level')).toBeVisible();
    await expect(page.locator('text=Peer Verified')).toBeVisible();
    await expect(page.locator('text=Preferred Vetting Level')).toBeVisible();
    await expect(page.locator('text=Project Verified')).toBeVisible();
    console.log('    ✅ Vetting requirements visible in review step');
    
    // Submit the request
    await page.click('button:has-text("Submit Request")');
    await page.waitForLoadState('networkidle');
    
    // Verify success
    await expect(page.locator('text=Request Created Successfully')).toBeVisible({ timeout: 10000 });
    console.log('✅ Gigwork request with vetting requirements created successfully!');

    // Step 4: Verify the request appears in the gigwork browser
    console.log('Step 4: Verifying request appears in gigwork browser...');
    await page.click('text=Browse Requests');
    await page.waitForLoadState('networkidle');
    
    // Look for our test request
    await expect(page.locator('text=Test Vetting Requirements Project')).toBeVisible({ timeout: 10000 });
    console.log('✅ Request visible in gigwork browser');
    
    // Click on the request to view details
    await page.click('text=Test Vetting Requirements Project');
    await page.waitForLoadState('networkidle');
    
    // Verify vetting requirements are displayed in the request details
    await expect(page.locator('text=Minimum Vetting Level')).toBeVisible();
    await expect(page.locator('text=Peer Verified')).toBeVisible();
    console.log('✅ Vetting requirements displayed in request details');

    console.log('🎉 VETTING REQUIREMENTS INTEGRATION TEST PASSED!');
    console.log('   - Database migration successful');
    console.log('   - Frontend form working correctly');
    console.log('   - Backend API handling vetting data');
    console.log('   - Data persistence confirmed');
    console.log('   - Display functionality verified');
  });

  test('should validate vetting level constraints', async ({ page }) => {
    console.log('🧪 Testing Vetting Level Validation');
    
    // Login and navigate to gigwork creation
    await page.fill('input[type="email"]', TEST_EMAIL);
    await page.fill('input[type="password"]', TEST_PASSWORD);
    await page.click('button[type="submit"]');
    await page.waitForLoadState('networkidle');
    
    await page.click('text=Earn');
    await page.waitForLoadState('networkidle');
    await page.click('text=Create Request');
    await page.waitForLoadState('networkidle');
    
    // Navigate to vetting requirements step
    await page.fill('input[name="title"]', 'Validation Test');
    await page.fill('textarea[name="description"]', 'Testing validation');
    await page.selectOption('select[name="category"]', 'development');
    await page.fill('input[name="budget"]', '1000');
    await page.click('button:has-text("Next")');
    await page.waitForTimeout(1000);
    
    await page.fill('input[name="duration"]', '15');
    await page.selectOption('select[name="urgency"]', 'low');
    await page.click('button:has-text("Next")');
    await page.waitForTimeout(1000);
    
    // Test validation: preferred level should be >= minimum level
    await page.selectOption('select[name="minimum_vetting_level"]', '4'); // Expert Verified
    await page.selectOption('select[name="preferred_vetting_level"]', '2'); // Peer Verified (invalid)
    
    await page.click('button:has-text("Next")');
    
    // Should show validation error
    const errorMessage = page.locator('text*=preferred level must be higher');
    if (await errorMessage.isVisible()) {
      console.log('✅ Validation working: Preferred level must be >= minimum level');
    }
    
    // Fix the validation error
    await page.selectOption('select[name="preferred_vetting_level"]', '5'); // Industry Certified
    await page.click('button:has-text("Next")');
    
    console.log('✅ Vetting level validation test passed');
  });
});
