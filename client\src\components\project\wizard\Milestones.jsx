import React, { useState, useEffect } from 'react';
import DatePicker from 'react-datepicker';
import { toast } from 'react-hot-toast';

const Milestones = ({ projectData, setProjectData }) => {
  const [newMilestone, setNewMilestone] = useState({
    name: '',
    description: '',
    deadline: null,
    deliverables: [],
    approval_criteria: '',
    status: 'pending'
  });
  const [editingIndex, setEditingIndex] = useState(null);
  const [showMilestoneForm, setShowMilestoneForm] = useState(false);
  const [newDeliverable, setNewDeliverable] = useState('');
  const [showPredefinedMilestones, setShowPredefinedMilestones] = useState(true);

  // Predefined milestones based on project type
  const predefinedMilestones = {
    common: [
      {
        name: 'Project Kickoff',
        description: 'Initial project setup and team onboarding',
        deliverables: ['Project plan', 'Team assignments', 'Initial documentation'],
        approval_criteria: 'All team members have access to project resources and understand their roles'
      },
      {
        name: 'Alpha Release',
        description: 'First working version with core functionality',
        deliverables: ['Core features implemented', 'Basic user interface', 'Internal testing report'],
        approval_criteria: 'Core functionality works as expected in controlled environment'
      },
      {
        name: 'Beta Release',
        description: 'Feature-complete version for testing',
        deliverables: ['All planned features implemented', 'User documentation', 'Test plan'],
        approval_criteria: 'All features work as expected and ready for external testing'
      },
      {
        name: 'Release Candidate',
        description: 'Final version before public release',
        deliverables: ['Bug fixes from beta testing', 'Final documentation', 'Marketing materials'],
        approval_criteria: 'No critical bugs, all documentation complete, ready for public release'
      },
      {
        name: 'Public Release',
        description: 'Official public launch',
        deliverables: ['Production-ready build', 'Launch announcement', 'Support plan'],
        approval_criteria: 'Successfully deployed to production environment and accessible to users'
      }
    ],
    game: [
      {
        name: 'Concept Approval',
        description: 'Game concept and design document',
        deliverables: ['Game design document', 'Concept art', 'Technical feasibility assessment'],
        approval_criteria: 'Design document approved by all stakeholders'
      },
      {
        name: 'Prototype',
        description: 'Playable prototype with core mechanics',
        deliverables: ['Playable build', 'Core mechanics implemented', 'Test results'],
        approval_criteria: 'Core gameplay loop is fun and engaging'
      }
    ],
    app: [
      {
        name: 'MVP',
        description: 'Minimum viable product with essential features',
        deliverables: ['Working app with core features', 'User feedback report'],
        approval_criteria: 'App performs essential functions correctly'
      },
      {
        name: 'App Store Submission',
        description: 'Preparation and submission to app stores',
        deliverables: ['App store listings', 'Screenshots', 'Privacy policy'],
        approval_criteria: 'App passes all store review guidelines'
      }
    ]
  };

  // Add a predefined milestone
  const addPredefinedMilestone = (milestone) => {
    const newMilestoneData = {
      ...milestone,
      deadline: null,
      status: 'pending'
    };

    const updatedMilestones = [...projectData.milestones, newMilestoneData];

    setProjectData({
      ...projectData,
      milestones: updatedMilestones
    });

    toast.success(`Added "${milestone.name}" milestone`);
  };

  // Add all common milestones
  const addAllCommonMilestones = () => {
    const projectType = projectData.project_type || 'other';
    const typeMilestones = predefinedMilestones[projectType] || [];

    // Combine common and type-specific milestones
    const allMilestones = [...predefinedMilestones.common, ...typeMilestones];

    // Add all milestones with deadlines spaced 30 days apart
    const updatedMilestones = allMilestones.map((milestone, index) => {
      const deadline = new Date();
      deadline.setDate(deadline.getDate() + (index + 1) * 30); // 30 days apart

      return {
        ...milestone,
        deadline,
        status: 'pending'
      };
    });

    setProjectData({
      ...projectData,
      milestones: updatedMilestones
    });

    toast.success('Added all common milestones');
  };

  // Automatically add predefined milestones if none exist
  useEffect(() => {
    if (projectData.milestones.length === 0) {
      addAllCommonMilestones();
    }
  }, []);

  // Add or update milestone
  const saveMilestoneHandler = () => {
    // Validate milestone
    if (!newMilestone.name) {
      toast.error('Milestone name is required');
      return;
    }

    const updatedMilestones = [...projectData.milestones];

    if (editingIndex !== null) {
      // Update existing milestone
      updatedMilestones[editingIndex] = newMilestone;
      toast.success('Milestone updated successfully');
    } else {
      // Add new milestone
      updatedMilestones.push(newMilestone);
      toast.success('Milestone added successfully');
    }

    setProjectData({
      ...projectData,
      milestones: updatedMilestones
    });

    // Reset form
    setNewMilestone({
      name: '',
      description: '',
      deadline: null,
      deliverables: [],
      approval_criteria: '',
      status: 'pending'
    });
    setEditingIndex(null);
    setShowMilestoneForm(false);
  };

  // Edit milestone
  const editMilestoneHandler = (index) => {
    setNewMilestone(projectData.milestones[index]);
    setEditingIndex(index);
    setShowMilestoneForm(true);
  };

  // Delete milestone
  const deleteMilestoneHandler = (index) => {
    const updatedMilestones = [...projectData.milestones];
    updatedMilestones.splice(index, 1);

    setProjectData({
      ...projectData,
      milestones: updatedMilestones
    });

    toast.success('Milestone deleted successfully');
  };

  // Add deliverable
  const addDeliverable = () => {
    if (!newDeliverable) return;

    setNewMilestone({
      ...newMilestone,
      deliverables: [...newMilestone.deliverables, newDeliverable]
    });

    setNewDeliverable('');
  };

  // Remove deliverable
  const removeDeliverable = (index) => {
    const updatedDeliverables = [...newMilestone.deliverables];
    updatedDeliverables.splice(index, 1);

    setNewMilestone({
      ...newMilestone,
      deliverables: updatedDeliverables
    });
  };

  // Format date
  const formatDate = (date) => {
    if (!date) return 'No deadline';
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="wizard-step-content">
      <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">Milestones & Specifications</h2>
      <p className="text-gray-600 dark:text-gray-300 mb-6">
        Define project milestones and deliverables.
      </p>

      <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-6">
        <div className="flex items-start gap-3">
          <div className="text-blue-600 dark:text-blue-400 text-xl">
            ℹ️
          </div>
          <div>
            <h4 className="font-semibold text-blue-900 dark:text-blue-100 mb-1">Smart Milestone Templates</h4>
            <p className="text-blue-800 dark:text-blue-200 text-sm">
              We've pre-populated common milestones based on your project type. You can customize these
              milestones, add new ones, or remove any that don't apply to your project.
            </p>
          </div>
        </div>
      </div>

      {!showMilestoneForm ? (
        <>
          <div className="flex justify-between items-center mb-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Milestones ({projectData.milestones.length})
            </h3>
            <div className="flex gap-3">
              <button
                type="button"
                className="px-4 py-2 border border-blue-600 text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors duration-200 flex items-center gap-2"
                onClick={() => setShowPredefinedMilestones(!showPredefinedMilestones)}
              >
                📋 Predefined Milestones
              </button>
              <button
                type="button"
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200 flex items-center gap-2"
                onClick={() => setShowMilestoneForm(true)}
              >
                ➕ Add Custom Milestone
              </button>
            </div>
          </div>

          {showPredefinedMilestones && (
            <div className="bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4 mb-6">
              <div className="flex justify-between items-center mb-4">
                <h4 className="font-medium text-gray-900 dark:text-white">Predefined Milestones</h4>
                <button
                  type="button"
                  className="px-3 py-1.5 bg-green-600 hover:bg-green-700 text-white text-sm rounded-md transition-colors duration-200"
                  onClick={addAllCommonMilestones}
                >
                  <i className="bi bi-plus-circle me-1"></i> Add All Common Milestones
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {predefinedMilestones.common.map((milestone, index) => (
                  <div key={`common-${index}`} className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4 h-full">
                    <h5 className="font-semibold text-gray-900 dark:text-white text-sm mb-2">{milestone.name}</h5>
                    <p className="text-gray-600 dark:text-gray-400 text-sm mb-3">{milestone.description}</p>
                    <button
                      type="button"
                      className="px-3 py-1.5 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-md transition-colors duration-200 flex items-center gap-1"
                      onClick={() => addPredefinedMilestone(milestone)}
                    >
                      <i className="bi bi-plus-lg"></i> Add
                    </button>
                  </div>
                ))}
              </div>

              {projectData.project_type && predefinedMilestones[projectData.project_type] && (
                <>
                  <h5 className="text-sm font-semibold text-gray-900 dark:text-white mt-6 mb-3">{projectData.project_type.charAt(0).toUpperCase() + projectData.project_type.slice(1)}-specific Milestones</h5>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {predefinedMilestones[projectData.project_type].map((milestone, index) => (
                      <div key={`type-${index}`} className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4 h-full">
                        <h5 className="font-semibold text-gray-900 dark:text-white text-sm mb-2">{milestone.name}</h5>
                        <p className="text-gray-600 dark:text-gray-400 text-sm mb-3">{milestone.description}</p>
                        <button
                          type="button"
                          className="px-3 py-1.5 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-md transition-colors duration-200 flex items-center gap-1"
                          onClick={() => addPredefinedMilestone(milestone)}
                        >
                          <i className="bi bi-plus-lg"></i> Add
                        </button>
                      </div>
                    ))}
                  </div>
                </>
              )}
            </div>
          )}

          {projectData.milestones.length === 0 ? (
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 text-blue-800 dark:text-blue-200">
              No milestones added yet. Add milestones to track project progress.
            </div>
          ) : (
            <div className="space-y-4">
              {projectData.milestones.map((milestone, index) => (
                <div key={index} className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 shadow-sm">
                  <div className="flex justify-between items-start mb-4">
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white">{milestone.name}</h4>
                    <div className="flex gap-2">
                      <button
                        type="button"
                        className="px-3 py-1.5 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-md transition-colors duration-200 flex items-center gap-1"
                        onClick={() => editMilestoneHandler(index)}
                      >
                        <i className="bi bi-pencil"></i> Edit
                      </button>
                      <button
                        type="button"
                        className="px-3 py-1.5 bg-red-600 hover:bg-red-700 text-white text-sm rounded-md transition-colors duration-200 flex items-center gap-1"
                        onClick={() => deleteMilestoneHandler(index)}
                      >
                        <i className="bi bi-trash"></i> Delete
                      </button>
                    </div>
                  </div>

                  {milestone.description && (
                    <p className="text-gray-600 dark:text-gray-400 text-sm mb-3">{milestone.description}</p>
                  )}

                  <div className="flex items-center text-gray-600 dark:text-gray-400 text-sm mb-4">
                    <i className="bi bi-calendar-event mr-2"></i>
                    <span className="font-medium">Deadline:</span>
                    <span className="ml-1">{formatDate(milestone.deadline)}</span>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <h5 className="text-sm font-semibold text-gray-900 dark:text-white mb-2">Deliverables</h5>
                      {milestone.deliverables.length > 0 ? (
                        <ul className="space-y-2">
                          {milestone.deliverables.map((deliverable, i) => (
                            <li key={i} className="flex items-center gap-2 text-sm text-gray-700 dark:text-gray-300">
                              <i className="bi bi-check-circle text-green-600 dark:text-green-400"></i>
                              <span>{deliverable}</span>
                            </li>
                          ))}
                        </ul>
                      ) : (
                        <p className="text-gray-500 dark:text-gray-400 text-sm">No deliverables specified</p>
                      )}
                    </div>

                    {milestone.approval_criteria && (
                      <div>
                        <h5 className="text-sm font-semibold text-gray-900 dark:text-white mb-2">Approval Criteria</h5>
                        <p className="text-gray-600 dark:text-gray-400 text-sm">{milestone.approval_criteria}</p>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}

          {projectData.milestones.length > 0 && (
            <div className="mt-8">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Project Timeline</h3>
              <div className="space-y-4">
                {projectData.milestones
                  .sort((a, b) => {
                    if (!a.deadline) return 1;
                    if (!b.deadline) return -1;
                    return new Date(a.deadline) - new Date(b.deadline);
                  })
                  .map((milestone, index) => (
                    <div key={index} className="flex items-start gap-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                      <div className="w-3 h-3 bg-blue-600 rounded-full mt-1 flex-shrink-0"></div>
                      <div className="flex-1">
                        <div className="text-sm font-medium text-blue-600 dark:text-blue-400 mb-1">
                          {formatDate(milestone.deadline)}
                        </div>
                        <div className="font-semibold text-gray-900 dark:text-white mb-1">{milestone.name}</div>
                        {milestone.description && (
                          <div className="text-gray-600 dark:text-gray-400 text-sm">
                            {milestone.description}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
              </div>
            </div>
          )}
        </>
      ) : (
        <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm">
          <div className="flex justify-between items-center px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              {editingIndex !== null ? 'Edit Milestone' : 'Add New Milestone'}
            </h3>
            <button
              type="button"
              className="px-3 py-1.5 bg-gray-600 hover:bg-gray-700 text-white text-sm rounded-md transition-colors duration-200"
              onClick={() => {
                setShowMilestoneForm(false);
                setEditingIndex(null);
                setNewMilestone({
                  name: '',
                  description: '',
                  deadline: null,
                  deliverables: [],
                  approval_criteria: '',
                  status: 'pending'
                });
              }}
            >
              Cancel
            </button>
          </div>

          <div className="p-6 space-y-6">
            <div>
              <label htmlFor="milestoneName" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Milestone Name <span className="text-red-600 dark:text-red-400">*</span>
              </label>
              <input
                type="text"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                id="milestoneName"
                value={newMilestone.name}
                onChange={(e) =>
                  setNewMilestone({ ...newMilestone, name: e.target.value })
                }
                placeholder="e.g. Alpha Release, Beta Testing, etc."
                required
              />
            </div>

            <div>
              <label htmlFor="milestoneDescription" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Description
              </label>
              <textarea
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                id="milestoneDescription"
                rows="2"
                value={newMilestone.description}
                onChange={(e) =>
                  setNewMilestone({ ...newMilestone, description: e.target.value })
                }
                placeholder="Optional description"
              ></textarea>
            </div>

            <div className="mb-3">
              <label htmlFor="milestoneDeadline" className="block text-sm font-medium text-foreground mb-2">
                Deadline
              </label>
              <DatePicker
                selected={
                  newMilestone.deadline ? new Date(newMilestone.deadline) : null
                }
                onChange={(date) =>
                  setNewMilestone({ ...newMilestone, deadline: date })
                }
                placeholderText="Select deadline"
                dateFormat="MMMM d, yyyy"
                id="milestoneDeadline"
                showPopperArrow={false}
              />
            </div>

            <div className="mb-3">
              <label className="form-label">Deliverables</label>
              <div className="input-group mb-2">
                <input
                  type="text"
                  className="form-control"
                  placeholder="Add a deliverable"
                  value={newDeliverable}
                  onChange={(e) => setNewDeliverable(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && addDeliverable()}
                />
                <button
                  className="btn btn-outline-secondary"
                  type="button"
                  onClick={addDeliverable}
                >
                  Add
                </button>
              </div>

              {newMilestone.deliverables.length > 0 ? (
                <ul className="list-group list-group-flush deliverable-list">
                  {newMilestone.deliverables.map((deliverable, index) => (
                    <li key={index} className="list-group-item px-0 py-2">
                      <div className="d-flex align-items-center justify-content-between">
                        <div>
                          <i className="bi bi-check-circle me-2 text-success"></i>
                          <span>{deliverable}</span>
                        </div>
                        <button
                          type="button"
                          className="btn btn-sm btn-link text-danger p-0"
                          onClick={() => removeDeliverable(index)}
                        >
                          <i className="bi bi-trash"></i>
                        </button>
                      </div>
                    </li>
                  ))}
                </ul>
              ) : (
                <div className="form-text">
                  No deliverables added yet. Examples: Completed UI design, Working
                  prototype, Final documentation, etc.
                </div>
              )}
            </div>

            <div className="mb-3">
              <label htmlFor="approvalCriteria" className="form-label">
                Approval Criteria
              </label>
              <textarea
                className="form-control"
                id="approvalCriteria"
                rows="3"
                value={newMilestone.approval_criteria}
                onChange={(e) =>
                  setNewMilestone({
                    ...newMilestone,
                    approval_criteria: e.target.value
                  })
                }
                placeholder="Define criteria for milestone approval"
              ></textarea>
              <div className="form-text">
                Specify what needs to be completed for this milestone to be considered
                done.
              </div>
            </div>

            <div className="d-flex justify-content-end">
              <button
                type="button"
                className="btn btn-primary"
                onClick={saveMilestoneHandler}
              >
                {editingIndex !== null ? 'Update Milestone' : 'Add Milestone'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Milestones;
