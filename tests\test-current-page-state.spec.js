import { test, expect } from '@playwright/test';

test.describe('Current Page State Analysis', () => {
  test('Analyze what is actually rendering on the page', async ({ page }) => {
    console.log('🚀 Analyzing current page state...');
    
    // Navigate to the site
    await page.goto('https://royalty.technology');
    
    // Wait for page to load
    await page.waitForTimeout(5000);
    
    // Get the full root content
    const rootContent = await page.evaluate(() => {
      const root = document.querySelector('#root');
      return root ? root.innerHTML : 'No root element found';
    });
    
    console.log('\n📄 Full Root Content:');
    console.log(rootContent);
    
    // Check for specific elements
    const elementChecks = await page.evaluate(() => {
      return {
        hasRoot: !!document.querySelector('#root'),
        hasLoadingSpinner: !!document.querySelector('[data-testid="simple-loading"]'),
        hasLoadingContainer: !!document.querySelector('.loading-container'),
        hasOverlayContainer: !!document.querySelector('[data-overlay-container]'),
        hasSkipLinks: !!document.querySelector('.sr-only'),
        hasMainContent: !!document.querySelector('#main-content'),
        hasNavigation: !!document.querySelector('nav'),
        hasHeader: !!document.querySelector('header'),
        hasFooter: !!document.querySelector('footer'),
        bodyClasses: document.body.className,
        htmlClasses: document.documentElement.className
      };
    });
    
    console.log('\n🔍 Element Analysis:');
    Object.entries(elementChecks).forEach(([key, value]) => {
      console.log(`  ${key}: ${value}`);
    });
    
    // Check for React components
    const reactCheck = await page.evaluate(() => {
      // Look for React fiber nodes
      const root = document.querySelector('#root');
      if (!root) return { hasReact: false, reason: 'No root element' };
      
      // Check for React fiber properties
      const fiberKey = Object.keys(root).find(key => key.startsWith('__reactFiber'));
      const containerKey = Object.keys(root).find(key => key.startsWith('__reactContainer'));
      
      return {
        hasReact: !!(fiberKey || containerKey),
        fiberKey: fiberKey || null,
        containerKey: containerKey || null,
        childElementCount: root.childElementCount,
        firstChildTagName: root.firstElementChild?.tagName || null
      };
    });
    
    console.log('\n⚛️ React Analysis:');
    Object.entries(reactCheck).forEach(([key, value]) => {
      console.log(`  ${key}: ${value}`);
    });
    
    // Take a screenshot for visual inspection
    await page.screenshot({ path: 'test-results/current-page-state.png', fullPage: true });
    console.log('\n📸 Screenshot saved to test-results/current-page-state.png');
    
    // Check if our debug messages from main.jsx are present
    const debugCheck = await page.evaluate(() => {
      return {
        jsExecutingFlag: window.JAVASCRIPT_EXECUTING,
        mainJsxFlag: window.MAIN_JSX_EXECUTED,
        titleModified: document.title.includes('MAIN.JSX EXECUTED')
      };
    });
    
    console.log('\n🔍 Debug Flags:');
    Object.entries(debugCheck).forEach(([key, value]) => {
      console.log(`  ${key}: ${value}`);
    });
    
    expect(true).toBeTruthy();
  });
});
