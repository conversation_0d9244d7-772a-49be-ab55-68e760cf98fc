/**
 * Template Loader - Secure Template Management
 * 
 * Handles loading, validation, and caching of agreement templates.
 * Ensures templates are properly formatted and contain required sections.
 */

import { TemplateError, ErrorFactory } from './errors/AgreementErrors.js';

export class TemplateLoader {
  constructor() {
    this.templateCache = new Map();
    this.templateMetadata = new Map();
    this.basePath = '/templates/v2/';
    
    // Template registry with metadata
    this.templates = {
      'standard': {
        filename: 'comprehensive_contributor_agreement_template.md',
        name: 'Comprehensive Contributor Agreement',
        description: 'Comprehensive contributor agreement with all important legal sections',
        version: '2.0.0',
        requiredSections: [
          'CONTRIBUTOR AGREEMENT',
          'Recitals',
          '1. Definitions',
          '2. Treatment of Confidential Information',
          '3. Ownership of Work Product',
          '4. Non-Disparagement',
          '5. Termination',
          '6. Equitable Remedies',
          '7. Assignment',
          '8. Waivers and Amendments',
          '9. Survival',
          '10. Status as Independent Contractor',
          '11. Representations and Warranties',
          '12. Indemnification',
          '13. Entire Agreement',
          '14. Governing Law',
          '15. Consent to Jurisdiction',
          '16. Settlement of Disputes',
          '17. Titles and Subtitles',
          '18. Opportunity to Consult',
          '19. Gender; Singular and Plural',
          '20. Notice',
          '21. Counterparts',
          'SCHEDULE A',
          'SCHEDULE B'
        ],
        requiredVariables: [
          'COMPANY_NAME',
          'COMPANY_LEGAL_NAME',
          'COMPANY_STATE',
          'COMPANY_ADDRESS',
          'COMPANY_SIGNER_NAME',
          'COMPANY_SIGNER_TITLE',
          'COMPANY_BILLING_EMAIL',
          'PROJECT_NAME',
          'PROJECT_DESCRIPTION',
          'CONTRIBUTOR_NAME',
          'CONTRIBUTOR_EMAIL',
          'EFFECTIVE_DATE'
        ]
      },
      'game-specific': {
        filename: 'game-contributor-agreement.md',
        name: 'Game Development Contributor Agreement',
        description: 'Specialized agreement for game development projects',
        version: '2.0.0',
        requiredSections: [
          // Same as standard plus game-specific sections
        ],
        requiredVariables: [
          // Same as standard plus game-specific variables
        ]
      },
      'software-specific': {
        filename: 'software-contributor-agreement.md',
        name: 'Software Development Contributor Agreement',
        description: 'Specialized agreement for software development projects',
        version: '2.0.0',
        requiredSections: [
          // Same as standard plus software-specific sections
        ],
        requiredVariables: [
          // Same as standard plus software-specific variables
        ]
      }
    };
  }

  /**
   * Load a template by type
   * @param {string} templateType - Type of template to load
   * @returns {Promise<string>} Template content
   * @throws {TemplateError} If template cannot be loaded
   */
  async loadTemplate(templateType) {
    try {
      // Check if template type exists
      if (!this.templates[templateType]) {
        throw ErrorFactory.createTemplateNotFoundError(templateType);
      }

      // Check cache first
      if (this.templateCache.has(templateType)) {
        return this.templateCache.get(templateType);
      }

      // Load template from file
      const templateInfo = this.templates[templateType];
      const templatePath = this.basePath + templateInfo.filename;
      
      let templateContent;
      
      try {
        // In browser environment, use fetch
        if (typeof window !== 'undefined') {
          const response = await fetch(templatePath);
          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }
          templateContent = await response.text();
        } else {
          // In Node.js environment, use fs
          const fs = await import('fs');
          const path = await import('path');
          const fullPath = path.join(process.cwd(), 'client/public', templatePath);
          templateContent = fs.readFileSync(fullPath, 'utf8');
        }
      } catch (error) {
        throw new TemplateError(
          `Failed to load template file: ${templatePath}`,
          templateType,
          [{ message: error.message, path: templatePath }]
        );
      }

      // Validate template content
      await this._validateTemplate(templateContent, templateType);

      // Cache the template
      this.templateCache.set(templateType, templateContent);
      this.templateMetadata.set(templateType, {
        ...templateInfo,
        loadedAt: new Date().toISOString(),
        size: templateContent.length
      });

      return templateContent;

    } catch (error) {
      if (error instanceof TemplateError) {
        throw error;
      }
      throw new TemplateError(
        `Unexpected error loading template: ${templateType}`,
        templateType,
        [{ message: error.message }]
      );
    }
  }

  /**
   * Get list of available templates
   * @returns {Promise<Array>} List of available templates with metadata
   */
  async getAvailableTemplates() {
    return Object.entries(this.templates).map(([type, info]) => ({
      type,
      name: info.name,
      description: info.description,
      version: info.version,
      cached: this.templateCache.has(type),
      lastLoaded: this.templateMetadata.get(type)?.loadedAt || null
    }));
  }

  /**
   * Get template metadata
   * @param {string} templateType - Type of template
   * @returns {Object} Template metadata
   */
  getTemplateMetadata(templateType) {
    const baseInfo = this.templates[templateType];
    const loadInfo = this.templateMetadata.get(templateType);
    
    if (!baseInfo) {
      throw ErrorFactory.createTemplateNotFoundError(templateType);
    }

    return {
      ...baseInfo,
      ...loadInfo,
      cached: this.templateCache.has(templateType)
    };
  }

  /**
   * Preload all templates (useful for performance)
   * @returns {Promise<Object>} Results of preloading all templates
   */
  async preloadAllTemplates() {
    const results = {};
    
    for (const templateType of Object.keys(this.templates)) {
      try {
        await this.loadTemplate(templateType);
        results[templateType] = { success: true };
      } catch (error) {
        results[templateType] = { 
          success: false, 
          error: error.message 
        };
      }
    }

    return results;
  }

  /**
   * Clear template cache
   * @param {string} templateType - Specific template to clear, or null for all
   */
  clearCache(templateType = null) {
    if (templateType) {
      this.templateCache.delete(templateType);
      this.templateMetadata.delete(templateType);
    } else {
      this.templateCache.clear();
      this.templateMetadata.clear();
    }
  }

  /**
   * Get cache statistics
   * @returns {Object} Cache statistics
   */
  getCacheStats() {
    return {
      totalTemplates: Object.keys(this.templates).length,
      cachedTemplates: this.templateCache.size,
      cacheHitRate: this.templateCache.size / Object.keys(this.templates).length,
      memoryUsage: Array.from(this.templateCache.values())
        .reduce((total, template) => total + template.length, 0),
      lastClearTime: this.lastClearTime || null
    };
  }

  // Private methods

  /**
   * Validate template content and structure
   * @private
   */
  async _validateTemplate(templateContent, templateType) {
    const templateInfo = this.templates[templateType];
    const errors = [];

    // Basic content validation
    if (!templateContent || templateContent.trim().length === 0) {
      errors.push({ message: 'Template content is empty' });
    }

    // Check for required sections
    templateInfo.requiredSections.forEach(section => {
      if (!templateContent.includes(section)) {
        errors.push({ 
          message: `Required section missing: ${section}`,
          section,
          type: 'MISSING_SECTION'
        });
      }
    });

    // Check for required variables (should be present as placeholders)
    templateInfo.requiredVariables.forEach(variable => {
      const variablePattern = new RegExp(`\\{\\{${variable}\\}\\}`, 'g');
      if (!variablePattern.test(templateContent)) {
        errors.push({ 
          message: `Required variable missing: {{${variable}}}`,
          variable,
          type: 'MISSING_VARIABLE'
        });
      }
    });

    // Check for forbidden hardcoded content
    const forbiddenContent = [
      'City of Gamers Inc.',
      'Village of The Ages',
      'Gynell Journigan',
      '1205 43rd Street',
      'Orange County',
      '32839'
    ];

    forbiddenContent.forEach(content => {
      if (templateContent.includes(content)) {
        errors.push({ 
          message: `Forbidden hardcoded content found: ${content}`,
          content,
          type: 'HARDCODED_CONTENT'
        });
      }
    });

    // Check for old-style placeholders that should be variables
    const oldStylePlaceholders = [
      '[Company Legal Name]',
      '[COMPANY NAME]',
      '[Project Name]',
      '[Company Address]'
    ];

    oldStylePlaceholders.forEach(placeholder => {
      if (templateContent.includes(placeholder)) {
        errors.push({ 
          message: `Old-style placeholder found: ${placeholder} (should use {{VARIABLE}} format)`,
          placeholder,
          type: 'OLD_STYLE_PLACEHOLDER'
        });
      }
    });

    if (errors.length > 0) {
      throw new TemplateError(
        `Template validation failed for ${templateType}`,
        templateType,
        errors
      );
    }
  }
}
