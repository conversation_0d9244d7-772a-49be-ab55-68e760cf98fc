import { test, expect } from '@playwright/test';

test.describe('Styling Verification Tests', () => {
  test('Verify Project Wizard Styling Improvements', async ({ page }) => {
    console.log('🚀 Starting styling verification test...');
    
    // Navigate directly to the project wizard
    await page.goto('https://royalty.technology');
    await page.waitForLoadState('networkidle');
    
    // Login with test credentials
    console.log('🔐 Logging in...');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'TestPassword123!');
    await page.click('button[type="submit"]');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    // Try to navigate to project wizard directly
    console.log('📝 Navigating to project wizard...');
    await page.goto('https://royalty.technology/#/project/wizard');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    // Take screenshot
    await page.screenshot({ 
      path: 'test-results/styling-verification.png',
      fullPage: true 
    });
    
    // Check page content
    const pageContent = await page.textContent('body');
    console.log(`📄 Page content length: ${pageContent.length}`);
    
    // Check for any form elements
    const allElements = page.locator('*');
    const elementCount = await allElements.count();
    console.log(`🔍 Total elements on page: ${elementCount}`);
    
    // Check for specific text content
    const hasProjectText = pageContent.includes('Project') || pageContent.includes('project');
    console.log(`📝 Contains project-related text: ${hasProjectText}`);
    
    // Check for styling classes
    const styledElements = page.locator('[class*="space-"], [class*="gap-"], [class*="grid"], [class*="flex"], [class*="rounded"], [class*="border"]');
    const styledCount = await styledElements.count();
    console.log(`🎨 Elements with Tailwind classes: ${styledCount}`);
    
    // Check for debug elements (should be 0)
    const debugElements = page.locator('[style*="background-color: red"], [style*="backgroundColor: red"]');
    const debugCount = await debugElements.count();
    console.log(`🐛 Debug elements found: ${debugCount}`);
    expect(debugCount).toBe(0);
    
    // Check for company information text
    const companyText = pageContent.includes('Company Information');
    console.log(`🏢 Contains "Company Information": ${companyText}`);
    
    // Check for thumbnail text
    const thumbnailText = pageContent.includes('Project Thumbnail');
    console.log(`🖼️ Contains "Project Thumbnail": ${thumbnailText}`);
    
    // Check for privacy text
    const privacyText = pageContent.includes('Project Privacy');
    console.log(`🔒 Contains "Project Privacy": ${privacyText}`);
    
    // Try alternative navigation
    console.log('🔄 Trying alternative navigation...');
    await page.goto('https://royalty.technology');
    await page.waitForLoadState('networkidle');
    
    // Look for any project creation buttons or links
    const createButtons = page.locator('text=/create|new|start|project/i');
    const createButtonCount = await createButtons.count();
    console.log(`➕ Create/Start buttons found: ${createButtonCount}`);
    
    if (createButtonCount > 0) {
      console.log('🔗 Found create buttons, trying to click...');
      await createButtons.first().click();
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(2000);
      
      // Take screenshot after clicking
      await page.screenshot({ 
        path: 'test-results/after-create-click.png',
        fullPage: true 
      });
      
      // Check for form elements after navigation
      const formElements = page.locator('input, select, textarea, button');
      const formCount = await formElements.count();
      console.log(`📝 Form elements after navigation: ${formCount}`);
      
      // Check for project wizard specific content
      const wizardContent = await page.textContent('body');
      const hasWizardContent = wizardContent.includes('Project Basics') || 
                              wizardContent.includes('Company Information') ||
                              wizardContent.includes('Project Name');
      console.log(`🧙 Has wizard content: ${hasWizardContent}`);
    }
    
    console.log('✅ Styling verification test completed!');
  });
});
