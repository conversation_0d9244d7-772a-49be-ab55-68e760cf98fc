import { test, expect } from '@playwright/test';

test.describe('Actual Project Wizard Tests', () => {
  test('Navigate to Real Project Creation Wizard', async ({ page }) => {
    console.log('🚀 Testing the ACTUAL project creation wizard...');
    
    // Navigate to the production site
    await page.goto('https://royalty.technology');
    await page.waitForLoadState('networkidle');
    
    // Login with test credentials
    console.log('🔐 Logging in...');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'TestPassword123!');
    await page.click('button[type="submit"]');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    // Try different ways to get to project creation
    console.log('🔍 Looking for project creation options...');
    
    // Method 1: Look for "Start" button
    const startButton = page.locator('text="Start"').first();
    if (await startButton.isVisible()) {
      console.log('📝 Found Start button, clicking...');
      await startButton.click();
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(2000);
    }
    
    // Method 2: Look for "Create Project" or similar
    const createProject = page.locator('text=/create.*project/i, text=/new.*project/i').first();
    if (await createProject.isVisible()) {
      console.log('📝 Found Create Project button, clicking...');
      await createProject.click();
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(2000);
    }
    
    // Method 3: Try direct navigation to project wizard
    console.log('🔗 Trying direct navigation to project wizard...');
    await page.goto('https://royalty.technology/project/wizard');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    // Method 4: Try hash-based routing
    await page.goto('https://royalty.technology/#/project/wizard');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    // Method 5: Try clicking "View All Projects" first
    const viewAllProjects = page.locator('text="View All Projects"');
    if (await viewAllProjects.isVisible()) {
      console.log('📋 Found "View All Projects" button, clicking...');
      await viewAllProjects.click();
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(2000);

      // Now look for create/new project button
      const createButtons = page.locator('button:has-text("New"), button:has-text("Create"), button:has-text("Add"), text=/create.*project/i, text=/new.*project/i');
      const createCount = await createButtons.count();
      console.log(`➕ Found ${createCount} potential create buttons`);

      if (createCount > 0) {
        console.log('🔗 Clicking first create button...');
        await createButtons.first().click();
        await page.waitForLoadState('networkidle');
        await page.waitForTimeout(2000);
      }
    }

    // Method 6: Try projects page directly
    await page.goto('https://royalty.technology/#/projects');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(1000);

    // Look for create/new project button on projects page
    const newProjectBtn = page.locator('button:has-text("New"), button:has-text("Create"), button:has-text("Add"), [class*="create"], [class*="new"]').first();
    if (await newProjectBtn.isVisible()) {
      console.log('➕ Found New Project button on projects page, clicking...');
      await newProjectBtn.click();
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(2000);
    }
    
    // Take screenshot of current state
    await page.screenshot({ 
      path: 'test-results/actual-wizard-current.png',
      fullPage: true 
    });
    
    // Check what's actually on the page
    const pageContent = await page.textContent('body');
    console.log(`📄 Current page content length: ${pageContent.length}`);
    
    // Look for wizard-specific content
    const hasProjectBasics = pageContent.includes('Project Basics');
    const hasCompanyInfo = pageContent.includes('Company Information');
    const hasProjectName = pageContent.includes('Project Name') || pageContent.includes('project name');
    const hasWizardSteps = pageContent.includes('Step') || pageContent.includes('step');
    
    console.log(`🧙 Has "Project Basics": ${hasProjectBasics}`);
    console.log(`🏢 Has "Company Information": ${hasCompanyInfo}`);
    console.log(`📝 Has "Project Name": ${hasProjectName}`);
    console.log(`📋 Has wizard steps: ${hasWizardSteps}`);
    
    // Check for duplicate labels
    const projectBasicsCount = (pageContent.match(/Project Basics/g) || []).length;
    console.log(`📊 "Project Basics" appears ${projectBasicsCount} times`);
    
    // Check for form elements
    const formInputs = page.locator('input, select, textarea');
    const inputCount = await formInputs.count();
    console.log(`📝 Form elements found: ${inputCount}`);
    
    // Check for debug elements
    const debugElements = page.locator('[style*="background-color: red"], [style*="backgroundColor: red"]');
    const debugCount = await debugElements.count();
    console.log(`🐛 Debug elements found: ${debugCount}`);
    
    // List all visible buttons to help understand navigation
    const buttons = page.locator('button:visible');
    const buttonCount = await buttons.count();
    console.log(`🔘 Visible buttons: ${buttonCount}`);
    
    for (let i = 0; i < Math.min(buttonCount, 10); i++) {
      const buttonText = await buttons.nth(i).textContent();
      console.log(`  Button ${i + 1}: "${buttonText}"`);
    }
    
    console.log('✅ Actual wizard navigation test completed!');
  });
});
