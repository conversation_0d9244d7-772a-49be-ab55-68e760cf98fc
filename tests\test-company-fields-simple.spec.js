import { test, expect } from '@playwright/test';

test.describe('Company Fields Visibility Test', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to login page and authenticate
    await page.goto('https://royalty.technology/login');
    await page.waitForLoadState('networkidle');
    
    // Login with test credentials
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'TestPassword123!');
    await page.click('button[type="submit"]');
    
    // Wait for authentication to complete
    await page.waitForURL('**/dashboard', { timeout: 10000 });
    await page.waitForLoadState('networkidle');
  });

  test('Check if new company fields are visible', async ({ page }) => {
    console.log('🔍 Testing company fields visibility...');

    // Navigate to project creation
    await page.goto('https://royalty.technology/project/create');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    // Take a screenshot of the initial page
    await page.screenshot({ path: 'project-create-initial.png', fullPage: true });
    
    // Look for the Company Information accordion
    const companyAccordion = page.locator('text=Company Information').first();
    if (await companyAccordion.isVisible()) {
      console.log('✓ Company Information accordion found');
      
      // Click to expand if needed (it should be expanded by default now)
      await companyAccordion.click();
      await page.waitForTimeout(1000);
      
      // Take screenshot after expanding
      await page.screenshot({ path: 'company-accordion-expanded.png', fullPage: true });
      
      // Check for all company fields
      const fields = [
        { name: 'Company Name', selector: 'input[label="Company Name"]' },
        { name: 'Company Address', selector: 'input[label="Company Address"]' },
        { name: 'State/Province', selector: 'input[label="State/Province"]' },
        { name: 'County/Region', selector: 'input[label="County/Region"]' },
        { name: 'City', selector: 'input[label="City"]' },
        { name: 'Contact Email', selector: 'input[label="Contact Email"]' },
        { name: 'Authorized Signer Name', selector: 'input[label="Authorized Signer Name"]' },
        { name: 'Signer Title', selector: 'input[label="Signer Title"]' }
      ];
      
      for (const field of fields) {
        const fieldElement = page.locator(field.selector);
        if (await fieldElement.isVisible()) {
          console.log(`✅ ${field.name} field is visible`);
        } else {
          console.log(`❌ ${field.name} field is NOT visible`);
        }
      }
      
      // Try alternative selectors for the new fields
      const alternativeSelectors = [
        'input[placeholder*="city"]',
        'input[placeholder*="contact email"]', 
        'input[placeholder*="signer"]',
        'input[placeholder*="title"]'
      ];
      
      console.log('🔍 Checking alternative selectors...');
      for (const selector of alternativeSelectors) {
        const elements = await page.locator(selector).count();
        console.log(`Selector "${selector}": ${elements} elements found`);
      }
      
    } else {
      console.log('❌ Company Information accordion not found');
    }
    
    // Take final screenshot
    await page.screenshot({ path: 'company-fields-final.png', fullPage: true });
    console.log('📸 Screenshots saved');
  });
});
