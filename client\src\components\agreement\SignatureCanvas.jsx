import React, { useRef, useState, useEffect } from 'react';
import SignaturePad from 'react-signature-canvas';

/**
 * SignatureCanvas Component
 * 
 * A component for capturing digital signatures using a canvas.
 * Supports drawing with mouse/touch, clearing, and saving the signature.
 */
const SignatureCanvas = ({ onSave, initialValue = null }) => {
  const sigCanvas = useRef(null);
  const [isEmpty, setIsEmpty] = useState(true);
  const [isTypedSignature, setIsTypedSignature] = useState(false);
  const [typedName, setTypedName] = useState('');
  const [selectedFont, setSelectedFont] = useState('Indie Flower');

  // Available signature fonts
  const signatureFonts = [
    { name: 'Indie Flower', label: 'Handwritten' },
    { name: 'Pacifico', label: 'Flowing' },
    { name: 'Sacramento', label: 'Elegant' },
    { name: 'Caveat', label: 'Casual' },
    { name: 'Dancing Script', label: 'Formal' }
  ];

  // Load fonts
  useEffect(() => {
    // Add Google Fonts link if not already present
    if (!document.getElementById('signature-fonts')) {
      const link = document.createElement('link');
      link.id = 'signature-fonts';
      link.rel = 'stylesheet';
      link.href = 'https://fonts.googleapis.com/css2?family=Indie+Flower&family=Pacifico&family=Sacramento&family=Caveat&family=Dancing+Script&display=swap';
      document.head.appendChild(link);
    }
  }, []);

  // Set initial value if provided
  useEffect(() => {
    if (initialValue && sigCanvas.current) {
      // If it's a data URL, load it into the canvas
      if (typeof initialValue === 'string' && initialValue.startsWith('data:')) {
        const img = new Image();
        img.onload = () => {
          const ctx = sigCanvas.current.getCanvas().getContext('2d');
          ctx.drawImage(img, 0, 0);
          setIsEmpty(false);
        };
        img.src = initialValue;
      }
    }
  }, [initialValue]);

  // Clear the signature canvas
  const clearSignature = () => {
    console.log('🧹 Clear signature clicked');
    if (sigCanvas.current) {
      sigCanvas.current.clear();
      setIsEmpty(true);
      console.log('✅ Signature cleared successfully');
    } else {
      console.error('❌ sigCanvas.current is null');
    }
    // Also clear typed signature
    setTypedName('');
    setIsEmpty(true);
  };

  // Save the signature as a data URL
  const saveSignature = () => {
    console.log('💾 Save signature clicked', { isTypedSignature, isEmpty, typedName });

    if (isTypedSignature) {
      if (!typedName.trim()) {
        console.error('❌ No typed name provided');
        return;
      }

      // For typed signatures, create a canvas with the typed name
      const canvas = document.createElement('canvas');
      canvas.width = 500;
      canvas.height = 200;
      const ctx = canvas.getContext('2d');

      // Set up the canvas
      ctx.fillStyle = 'white';
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      // Configure text style
      ctx.fillStyle = 'black';
      ctx.font = `48px "${selectedFont}"`;
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';

      // Draw the text
      ctx.fillText(typedName, canvas.width / 2, canvas.height / 2);

      // Convert to data URL and save
      const dataURL = canvas.toDataURL('image/png');
      console.log('✅ Typed signature saved');
      onSave(dataURL);
    } else if (sigCanvas.current && !isEmpty) {
      // For drawn signatures, get the data URL from the signature pad
      const dataURL = sigCanvas.current.toDataURL('image/png');
      console.log('✅ Drawn signature saved');
      onSave(dataURL);
    } else {
      console.error('❌ Cannot save signature - canvas is empty or not available');
    }
  };

  // Toggle between drawn and typed signature
  const toggleSignatureType = () => {
    setIsTypedSignature(!isTypedSignature);
    if (sigCanvas.current) {
      sigCanvas.current.clear();
    }
    setIsEmpty(true);
    setTypedName('');
  };

  // Check if signature pad is empty after each stroke
  const handleEndStroke = () => {
    if (sigCanvas.current) {
      setIsEmpty(sigCanvas.current.isEmpty());
    }
  };

  return (
    <div className="signature-canvas-container w-full">
      <div className="signature-type-toggle flex gap-2 mb-4 justify-center">
        <button
          type="button"
          className={`px-4 py-2 rounded-lg font-medium transition-colors ${
            !isTypedSignature
              ? 'bg-blue-600 text-white'
              : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
          }`}
          onClick={() => setIsTypedSignature(false)}
          style={{ pointerEvents: 'auto', zIndex: 10 }}
        >
          Draw Signature
        </button>
        <button
          type="button"
          className={`px-4 py-2 rounded-lg font-medium transition-colors ${
            isTypedSignature
              ? 'bg-blue-600 text-white'
              : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
          }`}
          onClick={() => setIsTypedSignature(true)}
          style={{ pointerEvents: 'auto', zIndex: 10 }}
        >
          Type Signature
        </button>
      </div>

      {isTypedSignature ? (
        <div className="typed-signature-container bg-white border-2 border-gray-300 rounded-lg p-4 mb-4">
          <div className="font-selector mb-4">
            <label htmlFor="signatureFont" className="block text-sm font-medium text-gray-700 mb-2">Font Style:</label>
            <select
              id="signatureFont"
              value={selectedFont}
              onChange={(e) => setSelectedFont(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {signatureFonts.map((font) => (
                <option key={font.name} value={font.name} style={{ fontFamily: font.name }}>
                  {font.label}
                </option>
              ))}
            </select>
          </div>
          <div
            className="typed-signature-preview text-center py-8 mb-4 border-b border-gray-200 text-3xl"
            style={{ fontFamily: selectedFont, minHeight: '100px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}
          >
            {typedName || 'Your Signature'}
          </div>
          <input
            type="text"
            value={typedName}
            onChange={(e) => {
              setTypedName(e.target.value);
              setIsEmpty(e.target.value.trim() === '');
            }}
            placeholder="Type your signature here"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      ) : (
        <div className="drawn-signature-container bg-white border-2 border-gray-300 rounded-lg p-4 mb-4">
          <SignaturePad
            ref={sigCanvas}
            canvasProps={{
              className: 'signature-pad border border-gray-200 rounded',
              width: 500,
              height: 200,
              style: { width: '100%', maxWidth: '500px', height: '200px' }
            }}
            onEnd={handleEndStroke}
          />
          <div className="signature-instructions text-center mt-2">
            <p className="text-sm text-gray-600">Sign using your mouse or touch screen</p>
          </div>
        </div>
      )}

      <div className="signature-actions flex gap-3 mt-4 justify-center">
        <button
          type="button"
          className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          onClick={clearSignature}
          disabled={isEmpty}
          style={{ pointerEvents: 'auto', zIndex: 10 }}
        >
          Clear
        </button>
        <button
          type="button"
          className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          onClick={saveSignature}
          disabled={isEmpty}
          style={{ pointerEvents: 'auto', zIndex: 10 }}
        >
          Save Signature
        </button>
      </div>
    </div>
  );
};

export default SignatureCanvas;
